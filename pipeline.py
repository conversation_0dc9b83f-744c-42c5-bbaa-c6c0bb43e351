# encoding: utf-8
import os
import argparse
import logging
import shutil
import subprocess
import sys
import zipfile
import re
import eve.frontend.utils as utils
from xml.etree import ElementTree as ET
from eve.base.command_toolkit import run_external_command
import requests
import json
from eve.base.dingding_util import DingDing
import yaml

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 资源相关目录地址
# WORKSPACE = os.getenv('WORKSPACE')
WORKSPACE = 'workspace'
CI_OUTPUT = os.path.join(WORKSPACE, 'ci_output')
TEMP_PATH = os.path.join(CI_OUTPUT, 'temp')
GRADLE_HOME = '/Users/<USER>/walle/gradle-8.2/bin/gradle'
ANDROID_SDK_HOME = '/Users/<USER>/walle/android-sdk-as-macosx'
JAVA_HOME = '/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home'
UNITY_APP_PATH = "/Applications/Unity/Hub/Editor/2022.3.45f1/Unity.app/Contents/MacOS/Unity"
ANDROID_NS = 'http://schemas.android.com/apk/res/android'
DING_TOKEN = 'bb0297782183fb9f91077afb5290cefbcda59f9b32e784050e63cb4761480130'
ZEUS_ANDROID_VERSION = "0.0.1-beta"
ZEUS_IOS_VERSION = "0.0.1-beta"
ZEUS_UNITY_VERSION = "0.0.1-beta"
COCOS_WORKSPACE = os.path.join(WORKSPACE, 'CocosCreator', 'ZeusSDK')
ZEUS_COCOS_VERSION = "0.0.1-beta"


def get_root_path():
    return os.path.dirname(os.path.dirname(os.path.realpath(__file__)))


def get_tag_name():
    tag_temp_name = os.getenv('gitlabBranch')
    _, tag_name = tag_temp_name.rsplit('/', 1)
    return tag_name


def get_token():
    return os.getenv('token')


def get_url():
    return os.getenv('url')


def get_tag_name_code():
    tag_temp_name = get_tag_name()
    tag_name = tag_temp_name[-9:]
    tag_name_code = re.sub("\\D", "", tag_name)
    return tag_name_code


def get_android_basic_path():
    return os.path.join(CI_OUTPUT, 'Android', 'ZeusSDK_v' + ZEUS_ANDROID_VERSION)


def get_ios_basic_path():
    return os.path.join(CI_OUTPUT, 'iOS', 'ZeusSDK_v' + ZEUS_IOS_VERSION)


def get_unity_basic_path():
    return os.path.join(CI_OUTPUT, 'Unity', 'ZeusSDK_v' + ZEUS_UNITY_VERSION)


def get_javadoc_basic_path():
    return os.path.join(CI_OUTPUT, 'Javadoc', 'ZeusSDK_v' + ZEUS_ANDROID_VERSION)


def get_unity_zeusinfoplist_path():
    return os.path.join(CI_OUTPUT, 'ZeusSDKInfoPlist', 'ZeusSDK_v' + ZEUS_UNITY_VERSION)


def get_cocos_basic_path():
    return os.path.join(CI_OUTPUT, 'CocosCreator', 'ZeusSDK_v' + ZEUS_COCOS_VERSION)


def zipDir(dirpath, out_path, root_dir_name=''):
    zip_file = zipfile.ZipFile(out_path, "w", zipfile.ZIP_DEFLATED)
    for path, dirnames, filenames in os.walk(dirpath):
        fpath = path.replace(dirpath, root_dir_name)  # 去掉目标根路径，只对目标文件夹下边的文件及文件夹进行压缩
        for filename in filenames:
            zip_file.write(os.path.join(path, filename), os.path.join(fpath, filename))
    zip_file.close()


def uploadsFile(privateToken, urlPath, file_path):
    headers = {
        'PRIVATE-TOKEN': privateToken
    }
    files = {'file': open(file_path, 'rb')}
    url = urlPath + "/uploads"
    try:
        response = requests.post(url, headers=headers, files=files)
        logger.info("statistic: request to {}, status code {}, response is {}.".format(
            url, response.status_code, response.content))
        return response.json()['markdown']
    except Exception as e:
        logger.info("statistic: request to {} failed, exception: {} .".format(url, e))


def sendRelease(token, url_path, markdown_lists):
    headers = {
        'Content-Type': 'application/json',
        'PRIVATE-TOKEN': token
    }
    # 添加更新日志
    tag_name = get_tag_name()
    test = os.popen('git cat-file -p {} | tail -n  +6'.format(tag_name))
    message = test.read()
    test.close()
    # 初始化变量
    ZEUS_ANDROID_VERSION_MD_GLOBAL = ZEUS_ANDROID_VERSION
    ZEUS_ANDROID_VERSION_MD_CHINA = ZEUS_ANDROID_VERSION
    if ZEUS_ANDROID_VERSION.endswith('SNAPSHOT'):
        ZEUS_ANDROID_VERSION_MD_GLOBAL = ZEUS_ANDROID_VERSION.replace('SNAPSHOT', 'GLOBAL-SNAPSHOT')
        ZEUS_ANDROID_VERSION_MD_CHINA = ZEUS_ANDROID_VERSION.replace('SNAPSHOT', 'CHINA-SNAPSHOT')
    else:
        ZEUS_ANDROID_VERSION_MD_GLOBAL = ZEUS_ANDROID_VERSION + '-GLOBAL'
        ZEUS_ANDROID_VERSION_MD_CHINA = ZEUS_ANDROID_VERSION + '-CHINA'

    # 修改描述，移除对 Javadoc 的引用
    description = "\n **资源链接:**\n <br> 1. Android: {} <br>2. iOS: {}<br>3. Unity: {}<br>4. Unity_ZeusSDKInfoPlist: {}<br>5. CocosCreator_Normal: {}<br>6. CocosCreator_Terser: {}<br>7. AndroidGradleDependency: 海外:implementation 'com.topjoy:zeussdk-android:{}' 国内:implementation 'com.topjoy:zeussdk-android:{}'".format(
        markdown_lists[0], markdown_lists[1], markdown_lists[2], markdown_lists[3], markdown_lists[4],
        markdown_lists[5], ZEUS_ANDROID_VERSION_MD_GLOBAL, ZEUS_ANDROID_VERSION_MD_CHINA)
    update_txt = message
    description = "{}\n{}".format(update_txt, description)
    # tag_name = get_tag_name
    data_dict = {"name": tag_name, "tag_name": tag_name, "description": description}
    data = json.dumps(data_dict)
    url = url_path + "/releases/"
    try:
        response = requests.post(url, headers=headers, data=data)
        logger.info("statistic: request to {}, status code {}, response is {}.".format(
            url, response.status_code, response.content))
    except Exception as e:
        logger.info("statistic: request to {} failed, exception: {} .".format(url, e))


# 项目组自己的CI流程
# tag命名规范， 分支名.日期.序号。 例如 release.230511.01
def build():
    utils.create_and_clean_path(CI_OUTPUT)
    readSDKVersion()
    android_basic_path = get_android_basic_path()
    ios_basic_path = get_ios_basic_path()
    unity_basic_path = get_unity_basic_path()
    javadoc_basic_path = get_javadoc_basic_path()
    unity_zeusinfoplist_path = get_unity_zeusinfoplist_path()
    cocos_basic_path = get_cocos_basic_path()  # 添加Cocos路径
    # writeReportURL()
    buildIOS(ios_basic_path)
    buildUnity(ios_basic_path, unity_basic_path, unity_zeusinfoplist_path)
    buildAndroid(android_basic_path, javadoc_basic_path)
    buildCocos(cocos_basic_path)  # 添加Cocos构建
    uploadPackage(android_basic_path, ios_basic_path, unity_basic_path, unity_zeusinfoplist_path, javadoc_basic_path,
                  cocos_basic_path)


def readSDKVersion():
    global ZEUS_ANDROID_VERSION
    global ZEUS_IOS_VERSION
    global ZEUS_UNITY_VERSION
    global ZEUS_COCOS_VERSION  # 添加全局变量

    android_version_path = os.path.join(WORKSPACE, 'Android', 'ZeusSDK', "ZeusSDK", "src", "main", "java", "com",
                                        "topjoy", "zeussdk", "version", "ZeusSdkVersion.java")
    ZEUS_ANDROID_VERSION = readFile(android_version_path, "SDK_VERSION = \"(.*?)\"")

    ios_verison_path = os.path.join(WORKSPACE, 'iOS', 'ZeusSDK', "ZeusSDK", "ZeusSDK", "Version", "ZeusSDKVersion.h")
    ZEUS_IOS_VERSION = readFile(ios_verison_path, "ZeusSDKVersion @\"(.*?)\"")

    unity_verison_path = os.path.join(WORKSPACE, 'Unity', 'ZeusSDK', "Assets", "ZeusSDK", "Core", "Runtime", "Utils",
                                      "ZeusSDKVersion.cs")
    ZEUS_UNITY_VERSION = readFile(unity_verison_path, "SDKVersion = \"(.*?)\"")

    # 读取CocosCreator版本号
    cocos_version_path = os.path.join(WORKSPACE, 'CocosCreator', 'ZeusSDK', "assets", "Helper", "ZeusSDKVersion.ts")
    ZEUS_COCOS_VERSION = readFile(cocos_version_path, "public static SDKVersion: string = \"(.*?)\"")


def readFile(file, str):
    with open(file, "r", encoding="utf-8") as f1:
        for HH in f1:
            result = re.findall(str, HH)
            if len(result) > 0:
                f1.close()
                return result[0]


def writeFile(file, old_str, new_str):
    with open(file, "r", encoding="utf-8") as f1, open("%s.bak" % file, "w", encoding="utf-8") as f2:
        for HH in f1:
            result = re.findall(old_str, HH)
            if len(result) > 0:
                f2.write(re.sub(result[0], new_str, HH))
            else:
                f2.write(HH)
        f1.close()
        f2.close()
        os.remove(file)
        os.rename("%s.bak" % file, file)


def writeReportURL():
    report_profile_path = os.path.join(WORKSPACE, '_continuous_integration_delivery', 'profiles', 'report.yaml')
    report_profile = yaml.load(open(report_profile_path))

    thinking_data_appid = report_profile.get('ThinkingDataAPPID')
    thinking_data_url = report_profile.get('ThinkingDataURL')
    sentry_android_dsn = report_profile.get('SentryAndroidDSN')
    sentry_ios_dsn = report_profile.get('SentryiOSDSN')

    android_profile_path = os.path.join(WORKSPACE, 'Android', 'ZeusSDK', "ZeusSDK", "src", "main", "java", "com",
                                        "topjoy", "zeussdk", "common", "MCProfile.java")
    writeFile(android_profile_path, "THINKING_APPID = \"(.*?)\"", thinking_data_appid)
    writeFile(android_profile_path, "THINKING_URL = \"(.*?)\"", thinking_data_url)
    writeFile(android_profile_path, "SENTRY_DSN = \"(.*?)\"", sentry_android_dsn)

    ios_profile_path = os.path.join(WORKSPACE, 'iOS', 'ZeusSDK', "ZeusSDK", "ZeusSDK", "Common", "MCProfile.h")
    writeFile(ios_profile_path, "THINKING_APPID @\"(.*?)\"", thinking_data_appid)
    writeFile(ios_profile_path, "THINKING_URL @\"(.*?)\"", thinking_data_url)
    writeFile(ios_profile_path, "SENTRY_DSN @\"(.*?)\"", sentry_ios_dsn)


def buildAndroid(android_basic_path, javadoc_basic_path):
    logger.info("=== build Android ===")
    android_workspace = os.path.join(WORKSPACE, 'Android', 'ZeusSDK')
    zeus_aar_build_path = os.path.join(android_workspace, "ZeusSDK", "build", "outputs", "aar")
    aar_path = os.path.join(android_basic_path, 'libs')
    utils.create_and_clean_path(aar_path)

    # 创建local.properties文件
    if os.path.exists(os.path.join(android_workspace, 'local.properties')):
        os.remove(os.path.join(android_workspace, 'local.properties'))
    with open(os.path.join(android_workspace, 'local.properties'), 'a+') as fw:
        fw.write('sdk.dir=' + ANDROID_SDK_HOME)
    with open(os.path.join(android_workspace, 'gradle.properties'), 'a') as f:
        f.write('\norg.gradle.java.home=' + JAVA_HOME)  # 添加java_home

    # 打开文件，并读取内容
    with open(os.path.join(android_workspace, 'local.properties'), 'r') as f:
        content = f.read()  # 读取文件内容

    print('local------------------------->')  # 打印文件内容
    print(content)  # 打印文件内容

    # 打开文件，并读取内容
    with open(os.path.join(android_workspace, 'gradle.properties'), 'r') as f:
        content = f.read()  # 读取文件内容

    print('gradle------------------------->')  # 打印文件内容
    print(content)  # 打印文件内容

    # 导出aar包资源
    run_external_command(GRADLE_HOME + " clean --project-dir " + android_workspace)

    run_external_command(GRADLE_HOME + " ZeusSDK:assembleGlobalRelease --project-dir " + android_workspace
                         + " --parallel --max-workers 16 --rerun-tasks")

    # run_external_command(GRADLE_HOME + " ZeusSDK:assembleChinaRelease --project-dir " + android_workspace
    #                      + " --parallel --max-workers 16 --rerun-tasks")

    # run_external_command(GRADLE_HOME + " assembleRelease --project-dir " + os.path.join(android_workspace, 'ZeusSDK')
    #                      + " --parallel --max-workers 16 --rerun-tasks")

    run_external_command(GRADLE_HOME + " publish --project-dir " + os.path.join(android_workspace, 'ZeusSDK')
                         + " --parallel --max-workers 16 --rerun-tasks")

    # run_external_command(GRADLE_HOME + " genJavadoc --project-dir " + os.path.join(android_workspace, 'ZeusSDK')
    #                      + " --parallel --max-workers 16 --rerun-tasks")

    shutil.copy(os.path.join(zeus_aar_build_path, 'ZeusSDK-global-release.aar'),
                os.path.join(aar_path, 'ZeusSDK-global-release.aar'))

    shutil.copy(os.path.join(zeus_aar_build_path, 'ZeusSDK-china-release.aar'),
                os.path.join(aar_path, 'ZeusSDK-china-release.aar'))
    # shutil.copytree(os.path.join(android_workspace, 'ZeusSDK','javadoc'), javadoc_basic_path)


def buildIOS(ios_basic_path):
    logger.info("=== build iOS ===")
    ios_workspace = os.path.join(WORKSPACE, 'iOS', 'ZeusSDK')
    framework_build_path = os.path.join(ios_workspace, 'build')

    framework_path = os.path.join(ios_basic_path, 'ZeusSDK', 'ZeusSDK.framework')
    bundle_path = os.path.join(ios_basic_path, 'ZeusSDK', 'ZeusSDK.bundle')

    xcframework_root = os.path.join(framework_build_path, 'XCFramework')

    utils.create_and_clean_path(TEMP_PATH)

    iphone_framework_path = framework_build_path + '/Release-iphoneos/ZeusSDK.framework'
    simulator_framework_path = framework_build_path + '/Release-iphonesimulator/ZeusSDK.framework'

    subprocess.check_call("xcodebuild -project " + ios_workspace + "/ZeusSDK.xcodeproj -configuration Release" +
                          " -arch arm64 -target ZeusSDK -sdk iphoneos clean build", shell=True)

    subprocess.check_call("xcodebuild -project " + ios_workspace + "/ZeusSDK.xcodeproj -configuration Release" +
                          " -arch x86_64 -target ZeusSDK -sdk iphonesimulator build", shell=True)

    shutil.copytree(os.path.join(iphone_framework_path, 'ZeusSDK.bundle'), bundle_path)

    removeBundles(iphone_framework_path)
    removeBundles(simulator_framework_path)

    shutil.copytree(iphone_framework_path, framework_path)

    # 复制工程目录资源文件
    shutil.copytree(os.path.join(ios_workspace, 'ZeusSDK', 'ThirdSDK'),
                    os.path.join(ios_basic_path, 'ZeusSDK', 'ThirdSDK'))
    shutil.rmtree(os.path.join(ios_basic_path, 'ZeusSDK', 'ThirdSDK', 'PlatformSDK', 'ApplovinSDK', 'AdMob',
                               'AppLovinMediationGoogleAdapter.xcframework'))
    shutil.rmtree(os.path.join(ios_basic_path, 'ZeusSDK', 'ThirdSDK', 'PlatformSDK', 'ApplovinSDK', 'Meta',
                               'AppLovinMediationFacebookAdapter.xcframework'))

    shutil.copytree(os.path.join(ios_workspace, 'ZeusSDK', 'ThirdSDKTool'),
                    os.path.join(ios_basic_path, 'ZeusSDK', 'ThirdSDKTool'))

    ZEUS_IOS_POD_VERSION = readFile(ios_workspace + "/ZeusSDK.podspec", "spec.version      = \"(.*?)\"")
    dest = "/Users/<USER>/rc_storage/zeus/zeussdk-ios/versions/v" + ZEUS_IOS_POD_VERSION
    if not os.path.exists(dest):
        os.makedirs(dest)
    else:
        # 当存在该pod版本时，不发布pod，避免覆盖
        return

    subprocess.check_call("xcodebuild -create-xcframework -framework " + iphone_framework_path + " -framework "
                          + simulator_framework_path + " -output " + xcframework_root + "/ZeusSDK.xcframework",
                          shell=True)

    shutil.copytree(bundle_path, xcframework_root + "/ZeusSDK.bundle")

    zipDir(xcframework_root, framework_build_path + "/ZeusSDK.zip", "Frameworks")

    shutil.copy(ios_workspace + "/ZeusSDK.podspec", dest + "/ZeusSDK.podspec")
    shutil.copy(framework_build_path + "/ZeusSDK.zip", dest + "/ZeusSDK.zip")


def removeBundles(frameworkPath):
    shutil.rmtree(frameworkPath + "/ZeusSDK.bundle")
    shutil.rmtree(frameworkPath + "/FCLocalization.bundle")
    shutil.rmtree(frameworkPath + "/FCResources.bundle")
    shutil.rmtree(frameworkPath + "/FreshchatModels.bundle")
    shutil.rmtree(frameworkPath + "/AppLovinSDKResources.bundle")


def buildUnity(ios_basic_path, unity_basic_path, unity_zeusinfoplist_path):
    logger.info("=== build Unity ===")
    unity_workspace = os.path.join(WORKSPACE, 'Unity', 'ZeusSDK')

    # 备份iOS工程目录资源
    unity_ios_zeussdk_root_path = os.path.join(unity_workspace, 'Assets', 'ZeusSDK', 'Plugins', 'iOS', 'ZeusSDK')
    unity_ios_zeussdk_second_path = os.path.join(unity_ios_zeussdk_root_path, "ZeusSDK")
    zeussdk_bundle_path = os.path.join(unity_ios_zeussdk_second_path, 'ZeusSDK.bundle')
    zeussdk_framework_path = os.path.join(unity_ios_zeussdk_second_path, 'ZeusSDK.framework')

    shutil.rmtree(zeussdk_bundle_path)
    shutil.rmtree(zeussdk_framework_path)

    zeusdk_product_bundle_path = os.path.join(ios_basic_path, 'ZeusSDK', 'ZeusSDK.bundle')
    zeusdk_product_framework_path = os.path.join(ios_basic_path, 'ZeusSDK', 'ZeusSDK.framework')

    shutil.copytree(zeusdk_product_bundle_path, zeussdk_bundle_path)
    shutil.copytree(zeusdk_product_framework_path, zeussdk_framework_path)

    utils.create_and_clean_path(unity_zeusinfoplist_path)
    shutil.copy(os.path.join(unity_ios_zeussdk_root_path, 'ZeusSDKInfo.plist'), unity_zeusinfoplist_path)
    # 导出UnityPackage
    utils.create_and_clean_path(unity_basic_path)
    run_external_command(UNITY_APP_PATH + " -quit -batchmode -projectPath " + unity_workspace
                         + " -executeMethod ExportPackage.ExportAssetsPackage " + unity_basic_path)


def buildCocos(cocos_basic_path):
    logger.info("=== build CocosCreator ===")
    utils.create_and_clean_path(cocos_basic_path)

    # 进入CocosCreator工作目录
    os.chdir(COCOS_WORKSPACE)

    # 检查 npm 命令是否可用
    try:
        subprocess.run(["npm", "--version"], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    except (subprocess.SubprocessError, FileNotFoundError):
        logger.error("npm 命令不可用，请确保已安装 Node.js")
        sys.exit(1)

    # 清理 node_modules 和 package-lock.json 以解决 npm 的 bug
    logger.info("清理 node_modules 和 package-lock.json...")
    if os.path.exists("node_modules"):
        shutil.rmtree("node_modules")
    if os.path.exists("package-lock.json"):
        os.remove("package-lock.json")

    # 安装依赖
    run_external_command("npm install")

    # 执行打包命令（不混淆）
    run_external_command("npm run build")

    # 创建非混淆版本的目录
    cocos_normal_path = os.path.join(cocos_basic_path, 'normal')
    os.makedirs(cocos_normal_path, exist_ok=True)

    # 检查输出目录，优先使用 ZeusSDK，如果不存在则使用 dist
    zeussdk_path = os.path.join(COCOS_WORKSPACE, 'ZeusSDK')
    dist_path = os.path.join(COCOS_WORKSPACE, 'dist')

    if os.path.exists(zeussdk_path):
        # 如果存在 ZeusSDK 目录，直接复制
        logger.info(f"复制 ZeusSDK 目录到非混淆版本: {cocos_normal_path}")
        shutil.copytree(zeussdk_path, os.path.join(cocos_normal_path, 'ZeusSDK'))
    elif os.path.exists(dist_path):
        # 如果只有 dist 目录，复制并重命名为 ZeusSDK
        logger.info(f"复制 dist 目录并重命名为 ZeusSDK: {cocos_normal_path}")
        shutil.copytree(dist_path, os.path.join(cocos_normal_path, 'ZeusSDK'))
    else:
        logger.error("未找到打包输出目录，既不是 ZeusSDK 也不是 dist")
        sys.exit(1)

    # 执行打包命令（混淆）
    run_external_command("npm run build:terser")

    # 创建混淆版本的目录
    cocos_terser_path = os.path.join(cocos_basic_path, 'terser')
    os.makedirs(cocos_terser_path, exist_ok=True)

    # 再次检查输出目录
    if os.path.exists(zeussdk_path):
        logger.info(f"复制 ZeusSDK 目录到混淆版本: {cocos_terser_path}")
        shutil.copytree(zeussdk_path, os.path.join(cocos_terser_path, 'ZeusSDK'))
    elif os.path.exists(dist_path):
        logger.info(f"复制 dist 目录并重命名为 ZeusSDK: {cocos_terser_path}")
        shutil.copytree(dist_path, os.path.join(cocos_terser_path, 'ZeusSDK'))
    else:
        logger.error("未找到打包输出目录，既不是 ZeusSDK 也不是 dist")
        sys.exit(1)

    # 返回到原始目录
    os.chdir(WORKSPACE)


def uploadPackage(android_basic_path, ios_basic_path, unity_basic_path, unity_zeusinfoplist_path, javadoc_basic_path,
                  cocos_basic_path):
    # 删除无用文件
    shutil.rmtree(TEMP_PATH)

    # 将工程资源目录打成压缩包
    zipDir(android_basic_path, android_basic_path + "_Android.zip", os.path.basename(android_basic_path) + '_Android')
    zipDir(ios_basic_path, ios_basic_path + "_iOS.zip", os.path.basename(ios_basic_path) + '_iOS')
    zipDir(unity_basic_path, unity_basic_path + "_Unity.zip", os.path.basename(unity_basic_path) + '_Unity')
    zipDir(unity_zeusinfoplist_path, unity_zeusinfoplist_path + "_UnityZeusSDKInfo.zip",
           os.path.basename(unity_zeusinfoplist_path) + '_UnityZeusSDKInfo')
    # zipDir(javadoc_basic_path, javadoc_basic_path + "_Javadoc.zip", os.path.basename(javadoc_basic_path) + '_Javadoc')

    # 分别压缩CocosCreator的混淆和非混淆版本
    cocos_normal_path = os.path.join(cocos_basic_path, 'normal')
    cocos_terser_path = os.path.join(cocos_basic_path, 'terser')

    zipDir(cocos_normal_path, cocos_basic_path + "_CocosCreator_Normal.zip",
           os.path.basename(cocos_basic_path) + '_CocosCreator_Normal')
    zipDir(cocos_terser_path, cocos_basic_path + "_CocosCreator_Terser.zip",
           os.path.basename(cocos_basic_path) + '_CocosCreator_Terser')

    # 删除src源文件
    shutil.rmtree(android_basic_path)
    shutil.rmtree(ios_basic_path)
    shutil.rmtree(unity_basic_path)
    shutil.rmtree(unity_zeusinfoplist_path)
    # shutil.rmtree(javadoc_basic_path)
    shutil.rmtree(cocos_basic_path)

    # 上传文件
    token = get_token()
    url_path = get_url()
    file_path_lists = [
        android_basic_path + "_Android.zip",
        ios_basic_path + "_iOS.zip",
        unity_basic_path + "_Unity.zip",
        unity_zeusinfoplist_path + "_UnityZeusSDKInfo.zip",
        # javadoc_basic_path + "_Javadoc.zip",
        cocos_basic_path + "_CocosCreator_Normal.zip",  # 非混淆版本
        cocos_basic_path + "_CocosCreator_Terser.zip"  # 混淆版本
    ]
    markdown_lists = []
    for i in file_path_lists:
        markdown_path = uploadsFile(token, url_path, i)
        markdown_lists.append(markdown_path)
    sendRelease(token, url_path, markdown_lists)


def send_notification():
    tag_name = get_tag_name()
    # 使用 startswith 检查是否以 'release' 或 'release3' 开头
    if tag_name.startswith('release'):
        fd = os.popen('git cat-file -p {} | tail -n  +6'.format(tag_name))
        description = fd.read()
        fd.close()
        notifier = DingDing(DING_TOKEN)
        message = "### 大家好,ZeusSDK新版本已发布!\n" + description
        notifier.send_markdown("ZeusSDK", message)
    else:
        print("[===Ding only from tag_name started with 'release'===]")


# @test.command(name="cocos")
# @click.option("--output-dir", "-o", default="./cocos_test_output", help="输出目录路径")
def tptestcocos(output_dir):
    """测试 CocosCreator 打包流程"""
    logger.info("=== 测试 CocosCreator 打包 ===")

    # 创建输出目录
    output_path = os.path.abspath(output_dir)
    if os.path.exists(output_path):
        logger.info(f"清理输出目录: {output_path}")
        shutil.rmtree(output_path)
    os.makedirs(output_path, exist_ok=True)

    logger.info(f"输出目录: {output_path}")

    # 获取 CocosCreator 工作目录
    cocos_workspace = os.path.join('/Users/<USER>/StudioProjects/ZeusSDK/CocosCreator/ZeusSDK')
    logger.info(f"CocosCreator 工作目录: {cocos_workspace}")

    # 检查工作目录是否存在
    if not os.path.exists(cocos_workspace):
        logger.error(f"CocosCreator 工作目录不存在: {cocos_workspace}")
        sys.exit(1)

    # 读取版本号
    try:
        cocos_version_path = os.path.join(
            cocos_workspace, "assets", "Helper", "ZeusSDKVersion.ts")
        if os.path.exists(cocos_version_path):
            version = readFile(cocos_version_path, "public static SDKVersion: string = \"(.*?)\"")
            logger.info(f"CocosCreator SDK 版本: {version}")
        else:
            logger.warning(f"版本文件不存在: {cocos_version_path}")
            version = "0.0.1-test"
    except Exception as e:
        logger.warning(f"读取版本号失败: {e}")
        version = "0.0.1-test"

    # 进入 CocosCreator 工作目录
    original_dir = os.getcwd()
    os.chdir(cocos_workspace)
    logger.info(f"切换到目录: {os.getcwd()}")

    try:
        # 检查 package.json 是否存在
        if not os.path.exists("package.json"):
            logger.error("package.json 文件不存在")
            sys.exit(1)

        # 检查 npm 命令是否可用
        try:
            subprocess.run(["npm", "--version"], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        except (subprocess.SubprocessError, FileNotFoundError):
            logger.error("npm 命令不可用，请确保已安装 Node.js")
            sys.exit(1)
        # 清理 node_modules 和 package-lock.json 以解决 npm 的 bug
        logger.info("清理 node_modules 和 package-lock.json...")
        if os.path.exists("node_modules"):
            shutil.rmtree("node_modules")
        if os.path.exists("package-lock.json"):
            os.remove("package-lock.json")

        # 安装依赖
        logger.info("安装依赖...")
        subprocess.run(["npm", "install"], check=True)

        # 执行打包命令（不混淆）
        logger.info("执行打包命令（不混淆）...")
        subprocess.run(["npm", "run", "build"], check=True)

        # 创建非混淆版本的目录
        normal_output_path = os.path.join(output_path, 'normal')
        os.makedirs(normal_output_path, exist_ok=True)

        # 复制非混淆版本的打包结果
        dist_path = os.path.join(cocos_workspace, 'dist')
        if os.path.exists(dist_path):
            logger.info(f"复制非混淆版本到: {normal_output_path}")
            shutil.copytree(dist_path, os.path.join(normal_output_path, 'dist'))
            # shutil.copy("package.json", os.path.join(normal_output_path, 'package.json'))
        else:
            logger.error(f"打包结果目录不存在: {dist_path}")

        # 执行打包命令（混淆）
        logger.info("执行打包命令（混淆）...")
        subprocess.run(["npm", "run", "build:terser"], check=True)

        # 创建混淆版本的目录
        terser_output_path = os.path.join(output_path, 'terser')
        os.makedirs(terser_output_path, exist_ok=True)

        # 复制混淆版本的打包结果
        if os.path.exists(dist_path):
            logger.info(f"复制混淆版本到: {terser_output_path}")
            shutil.copytree(dist_path, os.path.join(terser_output_path, 'dist'))
            # shutil.copy("package.json", os.path.join(terser_output_path, 'package.json'))
        else:
            logger.error(f"打包结果目录不存在: {dist_path}")

        # 创建压缩包
        logger.info("创建压缩包...")
        normal_zip_path = os.path.join(output_path, f"ZeusSDK_v{version}_CocosCreator_Normal.zip")
        terser_zip_path = os.path.join(output_path, f"ZeusSDK_v{version}_CocosCreator_Terser.zip")

        zipDir(normal_output_path, normal_zip_path, f"ZeusSDK_v{version}_CocosCreator_Normal")
        zipDir(terser_output_path, terser_zip_path, f"ZeusSDK_v{version}_CocosCreator_Terser")

        logger.info(f"非混淆版本压缩包: {normal_zip_path}")
        logger.info(f"混淆版本压缩包: {terser_zip_path}")

        logger.info("=== CocosCreator 打包测试完成 ===")

    except Exception as e:
        logger.error(f"CocosCreator 打包测试失败: {e}")
        raise
    finally:
        # 返回原始目录
        os.chdir(original_dir)


if __name__ == '__main__':
    # parser = argparse.ArgumentParser(description="ZeusSDK CI pipeline -- script")
    # parser.add_argument('-j', '--job', choices=['build', 'ding'])
    # args = parser.parse_args()
    # if args.job == 'build':
    #     print('build job start')
    #     build()
    # elif args.job == 'ding':
    #     print('ding send_notification job start')
    #     send_notification()
    print("test_cocos")
    tptestcocos('workspace/cocosout')

