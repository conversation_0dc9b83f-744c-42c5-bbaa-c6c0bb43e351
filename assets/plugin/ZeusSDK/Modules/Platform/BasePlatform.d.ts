import { Action } from "../../Core/Runtime/Action";
import Base from "../../Core/Runtime/Result/Base";
import { BillingParams } from "../../Modules/Platform/Typing";
import Ad from "../../Core/Runtime/Result/Advertiser";
import Network from "../../Core/Runtime/Result/Network";
import Questionnaire from "../../Core/Runtime/Result/Questionnaire";
import Notification from "../../Core/Runtime/Result/Notification";
export interface ShareOptions {
    title?: string;
    desc?: string;
    extra?: object;
    channel?: string;
    query?: string;
    templateId?: string;
    imageUrl?: string;
}
export declare abstract class BaseAdapter {
    protected abstract platform: string;
    abstract init(callback: Action<Base>): void;
    abstract login(callback: Action<Base>): void;
    abstract getUserInfo(callback: Action<Base>): void;
    abstract billing(billingParams: BillingParams, callback: Action<Base>): void;
    abstract showRewardedAd(placement: string, callback: Action<Ad>): void;
    abstract showInterstitialAd(placement: string, callback: Action<Ad>): void;
    abstract onNetworkStatusChange(callback: Action<Network>): void;
    abstract openQuestionnaire(activityId: string, callback: Action<Questionnaire>): void;
    abstract openCustomerService(callback: Action<Base>): void;
    abstract share(options: ShareOptions, callback: Action<Base>): void;
    abstract checkUpdate(): void;
    abstract setRankData(rankData: object): void;
    abstract hideOpenData(): void;
    abstract showRankList(option: object): void;
    abstract nativeToScene(): void;
    abstract subscribeMessage(tmplIds: string[], callback: Action<Notification>): void;
}
