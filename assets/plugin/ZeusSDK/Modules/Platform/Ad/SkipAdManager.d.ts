import AdManager from "./AdManager";
import { Action } from "../../../Core/Runtime/Action";
import Ad from "../../../Core/Runtime/Result/Advertiser";
import { AdMedia } from "../../../Common/ZeusConfig";
export declare class SkipAdManager extends AdManager {
    constructor(media: any, media_type: AdMedia);
    showRewardedAd(placement: string, callback: Action<Ad>): void;
    showInterstitialAd(placement: string, callback: Action<Ad>): void;
    protected complete(adUnitId: string, resp: any): void;
}
