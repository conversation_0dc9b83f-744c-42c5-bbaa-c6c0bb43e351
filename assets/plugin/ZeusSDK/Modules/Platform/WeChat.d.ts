import { BillingParams } from "../../Modules/Platform/Typing";
import Login from "../../Core/Runtime/Result/Login";
import { Action } from "../../Core/Runtime/Action";
import Base from "../../Core/Runtime/Result/Base";
import Ad from "../../Core/Runtime/Result/Advertiser";
import Notification from "../../Core/Runtime/Result/Notification";
import Network from "../../Core/Runtime/Result/Network";
import Questionnaire from "../../Core/Runtime/Result/Questionnaire";
import { BaseAdapter, ShareOptions } from "../../Modules/Platform/BasePlatform";
import { InitResult } from "../../Core/Runtime/Result/init";
export declare class WeChatAdapter extends BaseAdapter {
    private readonly wx;
    private readonly loginType;
    private _deviceInfo;
    private _adManager;
    private _analytics;
    private static instance;
    private readonly source_name;
    private _rankListOpenDataContext;
    private _shareStartTime;
    private hasAddedOnShowListener;
    private _isLogin;
    private _shareTicket;
    private _isInit;
    private constructor();
    private get deviceInfo();
    get platform(): any;
    static getInstance(): WeChatAdapter;
    init(callback: Action<Base>): void;
    login(callback: Action<InitResult | Login>): void;
    private doLogin;
    private loginSuccess;
    private doGetUserInfo;
    getUserInfo(callback: Action<Base>): void;
    private get billingType();
    private normalizeWeChatBillingResponse;
    private androidBillingProcess;
    private iosBillingProcess;
    private unsupportedBillingProcess;
    billing(billingParams: BillingParams, callback: Action<Base>): void;
    showRewardedAd(placement: string, callback: Action<Ad>): void;
    showInterstitialAd(placement: string, callback: Action<Ad>): void;
    onNetworkStatusChange(callback: Action<Network>): void;
    openCustomerService(): void;
    openQuestionnaire(activityId: string, callback: Action<Questionnaire>): void;
    checkUpdate(): void;
    setRankData(option: object): void;
    showRankList(dict: object): void;
    private requirePrivacyAuthorize;
    hideOpenData(): void;
    private getContext;
    subscribeMessage(tmplIds: string[], callback: Action<Notification>): void;
    sendSubscribeMessage(openId: string, tmplId: string, callback: Action<Base>): void;
    share(options: ShareOptions, callback: Action<Base>): void;
    nativeToScene(): void;
}
