import Status from "../../../Core/Runtime/Result/Status";
import Base, { BaseCode } from "../../../Core/Runtime/Result/Base";
export declare class LoginCode extends BaseCode {
    static readonly UserNotExist: Status;
    static readonly RegisterFail: Status;
    static readonly DeviceIsEmulator: Status;
    static readonly PassUnqualify: Status;
    static readonly PasswordUnqualify: Status;
    static readonly StatusFailByService: Status;
    static readonly PrefetchNumberFail: Status;
}
declare class LoginDetail {
    openid: string;
    session_key: string;
    unionid: string;
}
export default class Login extends Base {
    ID: number;
    Account: string;
    Token: string;
    LoginType: string;
    IsFirstRegister: boolean;
    Detail: LoginDetail;
    IsSkipAd: boolean;
    parse(resp: any): void;
}
export {};
