import Status from "../../../Core/Runtime/Result/Status";
import Base, { BaseCode } from "../../../Core/Runtime/Result/Base";
export declare class AdCode extends BaseCode {
    static ADDisplayed: Status;
    static ADDisplayFailed: Status;
    static ADClicked: Status;
    static ADHidden: Status;
    static ADLoaded: Status;
    static ADIsNotReady: Status;
    static ADUserRewarded: Status;
    static ADRevenuePaid: Status;
    static ADSDKNotInit: Status;
}
export declare class ADResult {
    adUnitId: string;
    networkName: string;
    networkPlacement: string;
    placement: string;
    requestLatencyMillis: number;
    creativeId: string;
    adReviewCreativeId: string;
    revenue: number;
    revenuePrecision: string;
    dspName: string;
    dspId: string;
}
export default class Ad extends Base {
    ADResult: ADResult;
    IsSucceeded: boolean;
    Uuid: string;
    Type: string;
    Platform: string;
    parse(resp: any): void;
}
