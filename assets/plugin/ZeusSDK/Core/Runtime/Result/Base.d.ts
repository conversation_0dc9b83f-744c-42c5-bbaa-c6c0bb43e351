import Status from "../../../Core/Runtime/Result/Status";
export declare class BaseCode {
    static readonly Success: Status;
    static readonly Cancel: Status;
    static readonly NetworkError: Status;
    static readonly UnknownHostError: Status;
    static readonly ParamError: Status;
    static readonly DataParseError: Status;
    static readonly SignError: Status;
    static readonly UserNotLogin: Status;
    static readonly SDKNotInit: Status;
    static readonly ZeusServiceFail: Status;
    static readonly ThirdSDKReturnErrorCode: Status;
    static readonly ZeusSDKFail: Status;
    static readonly AntiDupClick: Status;
}
export default class Base {
    Status: Status;
    IsSucceeded: boolean;
    parse(result: any): void;
}
