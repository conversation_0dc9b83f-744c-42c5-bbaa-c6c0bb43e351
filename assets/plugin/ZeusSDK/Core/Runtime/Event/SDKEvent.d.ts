import { EventCode } from "./EventCode";
export declare class SDKEvent {
    static readonly net_name: string;
    static readonly event_code: string;
    static readonly event_description: string;
    static readonly code: string;
    static readonly msg: string;
    static readonly result: string;
    static readonly native_code: string;
    static readonly backend_code: string;
    static readonly backend_message: string;
    static readonly net_time: string;
    static readonly sdk_version: string;
    static readonly tag_version: string;
    static readonly user_id: string;
    static readonly game_id: string;
    static readonly device_memory: string;
    static readonly base_url: string;
    static readonly trace_id: string;
    static readonly zeus_device_id: string;
    /** event fields */
    eventName: string | null;
    eventCode: string | null;
    zeusMsg: string | null;
    zeusCode: number;
    zeusResult: string | null;
    nativeCode: string | null;
    backendCode: string | null;
    backendMessage: string | null;
    description: string | null;
    netTime: string | null;
    traceID: string | null;
    GetJson(): object;
    constructor(_event: EventCode, json: object);
    /**
     * 事件打点
     */
    Track(): void;
}
