export declare class EventCode {
    static readonly INIT: EventCode;
    static readonly LOGIN: EventCode;
    static readonly RE_LOGIN: EventCode;
    static readonly LOGIN_VIEW: EventCode;
    static readonly LOGOUT: EventCode;
    static readonly DELETE_ACCOUNT: EventCode;
    static readonly GET_IN_APP_SKU_DETAIL: EventCode;
    static readonly GET_SUBS_SKU_DETAIL: EventCode;
    static readonly GET_GOODS_LIST: EventCode;
    static readonly PAY: EventCode;
    static readonly ALIPAY: EventCode;
    static readonly WXPAY: EventCode;
    static readonly SHARE: EventCode;
    static readonly SHARE_WITH_PLATFORM: EventCode;
    static readonly UPLOAD_ROLE: EventCode;
    static readonly GET_BIND_LIST: EventCode;
    static readonly THIRD_LOGIN: EventCode;
    static readonly BIND: EventCode;
    static readonly USER_CENTER: EventCode;
    static readonly SDK_SHOW_DIALOG: EventCode;
    static readonly EXIT_DIALOG: EventCode;
    static readonly SHOW_CUSTOMER_SERVICE_CHAT: EventCode;
    static readonly SHOW_CUSTOMER_SERVICE_FAQS: EventCode;
    static readonly SET_CUSTOMER_SERVICE_USER_INFO: EventCode;
    static readonly QUESTIONNAIRE: EventCode;
    static readonly OPEN_SUBSCRIPTION: EventCode;
    static readonly TRANSLATION: EventCode;
    static readonly REVIEW: EventCode;
    static readonly REVIEW_IN_APP: EventCode;
    static readonly GET_UNREAD_MSG_FLAG: EventCode;
    static readonly GET_CURRENT_FCM_TOKEN: EventCode;
    static readonly REAL_NAME: EventCode;
    static readonly TIME_LIMIT: EventCode;
    static readonly PAY_LIMIT: EventCode;
    static readonly GET_REMAINING_TIME: EventCode;
    static readonly IS_GOOGLE_CONNECTED: EventCode;
    static readonly OPEN_MAX_DEBUGGER: EventCode;
    static readonly PRELOAD_APPLOVIN_AD: EventCode;
    static readonly SET_ADULT_FLAG: EventCode;
    static readonly SET_CONSENT_FLAG: EventCode;
    static readonly SET_DO_NOT_SELL_FLAG: EventCode;
    static readonly SHOW_APPLOVIN_MAX_REWARD_AD: EventCode;
    static readonly DEFER_APPLOVIN_INIT: EventCode;
    static readonly DO_APPLOVIN_INIT: EventCode;
    static readonly RECOVER_ORDER: EventCode;
    static readonly IS_JAIL_BROKEN: EventCode;
    static readonly PAY_CONSUME: EventCode;
    static readonly FACEBOOK_INFO: EventCode;
    static readonly WX_SHARE: EventCode;
    static readonly REGISTER: EventCode;
    code: number;
    description: string;
    constructor(code: number, description: string);
}
