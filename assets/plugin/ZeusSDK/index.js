import { sys } from 'cc';

var Module;
(function (Module) {
    Module["THINKING"] = "thinking";
    Module["GRAVITY"] = "gravity";
    Module["WECHAT"] = "wechat";
    Module["BYTE_DANCE"] = "byteDance";
})(Module || (Module = {}));
var AdMedia;
(function (AdMedia) {
    AdMedia["WECHAT"] = "wechat";
    AdMedia["BYTE_DANCE"] = "byteDance";
})(AdMedia || (AdMedia = {}));
var AdType;
(function (AdType) {
    AdType["REWARD"] = "reward";
    AdType["INTERSTITIAL"] = "interstitial";
    AdType["REWARD_INTERSTITIAL"] = "reward_interstitial";
})(AdType || (AdType = {}));
let ZeusConfig = {
    default: {
        "thinking": [
            {
                "server_url": "https://log-data.topjoy.com/",
                "app_id": ""
            },
            {
                "server_url": "https://log-data.topjoy.com/",
                "app_id": "",
                "is_error_tracker": true
            }
        ],
        "wechat": {
            "app_id": "",
            "billing_env": 0,
            "billing_qrcode_image_url": "https://xxx.png"
        },
        "gravity": {
            "access_token": "",
            "name": "ge",
            "debug": false
        },
        "byteDance": {
            "app_id": "",
            "billing_env": 0
        }
    }
};
function getConfig() {
    return ZeusConfig["default"];
}
function getModuleConfigValue(module_name, module_sub_key) {
    let conf = getConfig();
    return conf[module_name][module_sub_key];
}

// copy from cc.private._pal_system_info_enum_type_platform__Platform
var RuntimePlatform;
(function (RuntimePlatform) {
    RuntimePlatform["UNKNOWN"] = "UNKNOWN";
    RuntimePlatform["EDITOR_PAGE"] = "EDITOR_PAGE";
    RuntimePlatform["EDITOR_CORE"] = "EDITOR_CORE";
    RuntimePlatform["MOBILE_BROWSER"] = "MOBILE_BROWSER";
    RuntimePlatform["DESKTOP_BROWSER"] = "DESKTOP_BROWSER";
    RuntimePlatform["WIN32"] = "WIN32";
    RuntimePlatform["ANDROID"] = "ANDROID";
    RuntimePlatform["IOS"] = "IOS";
    RuntimePlatform["MACOS"] = "MACOS";
    RuntimePlatform["OHOS"] = "OHOS";
    RuntimePlatform["OPENHARMONY"] = "OPENHARMONY";
    RuntimePlatform["WECHAT_GAME"] = "WECHAT_GAME";
    RuntimePlatform["WECHAT_MINI_PROGRAM"] = "WECHAT_MINI_PROGRAM";
    RuntimePlatform["BAIDU_MINI_GAME"] = "BAIDU_MINI_GAME";
    RuntimePlatform["XIAOMI_QUICK_GAME"] = "XIAOMI_QUICK_GAME";
    RuntimePlatform["ALIPAY_MINI_GAME"] = "ALIPAY_MINI_GAME";
    RuntimePlatform["TAOBAO_CREATIVE_APP"] = "TAOBAO_CREATIVE_APP";
    RuntimePlatform["TAOBAO_MINI_GAME"] = "TAOBAO_MINI_GAME";
    RuntimePlatform["BYTEDANCE_MINI_GAME"] = "BYTEDANCE_MINI_GAME";
    RuntimePlatform["OPPO_MINI_GAME"] = "OPPO_MINI_GAME";
    RuntimePlatform["VIVO_MINI_GAME"] = "VIVO_MINI_GAME";
    RuntimePlatform["HUAWEI_QUICK_GAME"] = "HUAWEI_QUICK_GAME";
    RuntimePlatform["COCOSPLAY"] = "COCOSPLAY";
    RuntimePlatform["LINKSURE_MINI_GAME"] = "LINKSURE_MINI_GAME";
    RuntimePlatform["QTT_MINI_GAME"] = "QTT_MINI_GAME";
})(RuntimePlatform || (RuntimePlatform = {}));
class _Device {
    constructor() {
        this._isNative = null;
    }
    get runtimePlatform() {
        return sys.platform;
    }
    get os() {
        return sys.os;
    }
    get networkType() {
        return sys.getNetworkType();
    }
    get deviceId() {
        var _a;
        let device_id;
        if (this.isBrowser() || this.isMiniGame()) {
            const device_id_key = "pseudo_device_id";
            const stringEmpty = "";
            device_id = (_a = sys.localStorage.getItem(device_id_key)) !== null && _a !== void 0 ? _a : stringEmpty;
            if (device_id == stringEmpty) {
                // 生成8位16进制数字构成的id
                for (let i = 0; i < 32; i++) {
                    device_id += Math.floor(Math.random() * 16).toString(16);
                }
                sys.localStorage.setItem(device_id_key, device_id);
            }
        }
        else {
            // ToDo 桥接Android， iOS原生Zeus实现。
            throw new Error("not supported yet");
        }
        return device_id;
    }
    isNative() {
        if (this._isNative == null) {
            let platform = this.runtimePlatform;
            this._isNative = (platform == RuntimePlatform.ANDROID
                || platform == RuntimePlatform.MACOS
                || platform == RuntimePlatform.IOS
                || platform == RuntimePlatform.WIN32);
        }
        return this._isNative;
    }
    isMobile() {
        let platform = this.runtimePlatform;
        return (platform == RuntimePlatform.ANDROID
            || platform == RuntimePlatform.IOS);
    }
    isWeChat() {
        let platform = this.runtimePlatform;
        return platform == RuntimePlatform.WECHAT_GAME;
    }
    isByteDance() {
        let platform = this.runtimePlatform;
        return platform == RuntimePlatform.BYTEDANCE_MINI_GAME;
    }
    isBrowser() {
        let platform = this.runtimePlatform;
        return platform == RuntimePlatform.EDITOR_CORE
            || platform == RuntimePlatform.EDITOR_PAGE
            || platform == RuntimePlatform.DESKTOP_BROWSER
            || platform == RuntimePlatform.MOBILE_BROWSER;
    }
    isMiniGame() {
        let platform = this.runtimePlatform;
        return (platform == RuntimePlatform.WECHAT_GAME || platform == RuntimePlatform.BYTEDANCE_MINI_GAME);
    }
    isIOS() {
        return this.os == sys.OS.IOS;
    }
    isAndroid() {
        return this.os == sys.OS.ANDROID;
    }
}
const Device = new _Device();

/*

TypeScript Md5
==============

Based on work by
* Joseph Myers: http://www.myersdaily.org/joseph/javascript/md5-text.html
* André Cruz: https://github.com/satazor/SparkMD5
* Raymond Hill: https://github.com/gorhill/yamd5.js

Effectively a TypeScrypt re-write of Raymond Hill JS Library

The MIT License (MIT)

Copyright (C) 2014 Raymond Hill

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.



            DO WHAT YOU WANT TO PUBLIC LICENSE
                    Version 2, December 2004

 Copyright (C) 2015 André Cruz <<EMAIL>>

 Everyone is permitted to copy and distribute verbatim or modified
 copies of this license document, and changing it is allowed as long
 as the name is changed.

            DO WHAT YOU WANT TO PUBLIC LICENSE
   TERMS AND CONDITIONS FOR COPYING, DISTRIBUTION AND MODIFICATION

  0. You just DO WHAT YOU WANT TO.


*/
class Md5 {
    static hashStr(str, raw = false) {
        return this.onePassHasher
            .start()
            .appendStr(str)
            .end(raw);
    }
    static hashAsciiStr(str, raw = false) {
        return this.onePassHasher
            .start()
            .appendAsciiStr(str)
            .end(raw);
    }
    static _hex(x) {
        const hc = Md5.hexChars;
        const ho = Md5.hexOut;
        let n;
        let offset;
        let j;
        let i;
        for (i = 0; i < 4; i += 1) {
            offset = i * 8;
            n = x[i];
            for (j = 0; j < 8; j += 2) {
                ho[offset + 1 + j] = hc.charAt(n & 0x0F);
                n >>>= 4;
                ho[offset + 0 + j] = hc.charAt(n & 0x0F);
                n >>>= 4;
            }
        }
        return ho.join('');
    }
    static _md5cycle(x, k) {
        let a = x[0];
        let b = x[1];
        let c = x[2];
        let d = x[3];
        // ff()
        a += (b & c | ~b & d) + k[0] - 680876936 | 0;
        a = (a << 7 | a >>> 25) + b | 0;
        d += (a & b | ~a & c) + k[1] - 389564586 | 0;
        d = (d << 12 | d >>> 20) + a | 0;
        c += (d & a | ~d & b) + k[2] + 606105819 | 0;
        c = (c << 17 | c >>> 15) + d | 0;
        b += (c & d | ~c & a) + k[3] - 1044525330 | 0;
        b = (b << 22 | b >>> 10) + c | 0;
        a += (b & c | ~b & d) + k[4] - 176418897 | 0;
        a = (a << 7 | a >>> 25) + b | 0;
        d += (a & b | ~a & c) + k[5] + 1200080426 | 0;
        d = (d << 12 | d >>> 20) + a | 0;
        c += (d & a | ~d & b) + k[6] - 1473231341 | 0;
        c = (c << 17 | c >>> 15) + d | 0;
        b += (c & d | ~c & a) + k[7] - 45705983 | 0;
        b = (b << 22 | b >>> 10) + c | 0;
        a += (b & c | ~b & d) + k[8] + 1770035416 | 0;
        a = (a << 7 | a >>> 25) + b | 0;
        d += (a & b | ~a & c) + k[9] - 1958414417 | 0;
        d = (d << 12 | d >>> 20) + a | 0;
        c += (d & a | ~d & b) + k[10] - 42063 | 0;
        c = (c << 17 | c >>> 15) + d | 0;
        b += (c & d | ~c & a) + k[11] - 1990404162 | 0;
        b = (b << 22 | b >>> 10) + c | 0;
        a += (b & c | ~b & d) + k[12] + 1804603682 | 0;
        a = (a << 7 | a >>> 25) + b | 0;
        d += (a & b | ~a & c) + k[13] - 40341101 | 0;
        d = (d << 12 | d >>> 20) + a | 0;
        c += (d & a | ~d & b) + k[14] - 1502002290 | 0;
        c = (c << 17 | c >>> 15) + d | 0;
        b += (c & d | ~c & a) + k[15] + 1236535329 | 0;
        b = (b << 22 | b >>> 10) + c | 0;
        // gg()
        a += (b & d | c & ~d) + k[1] - 165796510 | 0;
        a = (a << 5 | a >>> 27) + b | 0;
        d += (a & c | b & ~c) + k[6] - 1069501632 | 0;
        d = (d << 9 | d >>> 23) + a | 0;
        c += (d & b | a & ~b) + k[11] + 643717713 | 0;
        c = (c << 14 | c >>> 18) + d | 0;
        b += (c & a | d & ~a) + k[0] - 373897302 | 0;
        b = (b << 20 | b >>> 12) + c | 0;
        a += (b & d | c & ~d) + k[5] - 701558691 | 0;
        a = (a << 5 | a >>> 27) + b | 0;
        d += (a & c | b & ~c) + k[10] + 38016083 | 0;
        d = (d << 9 | d >>> 23) + a | 0;
        c += (d & b | a & ~b) + k[15] - 660478335 | 0;
        c = (c << 14 | c >>> 18) + d | 0;
        b += (c & a | d & ~a) + k[4] - 405537848 | 0;
        b = (b << 20 | b >>> 12) + c | 0;
        a += (b & d | c & ~d) + k[9] + 568446438 | 0;
        a = (a << 5 | a >>> 27) + b | 0;
        d += (a & c | b & ~c) + k[14] - 1019803690 | 0;
        d = (d << 9 | d >>> 23) + a | 0;
        c += (d & b | a & ~b) + k[3] - 187363961 | 0;
        c = (c << 14 | c >>> 18) + d | 0;
        b += (c & a | d & ~a) + k[8] + 1163531501 | 0;
        b = (b << 20 | b >>> 12) + c | 0;
        a += (b & d | c & ~d) + k[13] - 1444681467 | 0;
        a = (a << 5 | a >>> 27) + b | 0;
        d += (a & c | b & ~c) + k[2] - 51403784 | 0;
        d = (d << 9 | d >>> 23) + a | 0;
        c += (d & b | a & ~b) + k[7] + 1735328473 | 0;
        c = (c << 14 | c >>> 18) + d | 0;
        b += (c & a | d & ~a) + k[12] - 1926607734 | 0;
        b = (b << 20 | b >>> 12) + c | 0;
        // hh()
        a += (b ^ c ^ d) + k[5] - 378558 | 0;
        a = (a << 4 | a >>> 28) + b | 0;
        d += (a ^ b ^ c) + k[8] - 2022574463 | 0;
        d = (d << 11 | d >>> 21) + a | 0;
        c += (d ^ a ^ b) + k[11] + 1839030562 | 0;
        c = (c << 16 | c >>> 16) + d | 0;
        b += (c ^ d ^ a) + k[14] - 35309556 | 0;
        b = (b << 23 | b >>> 9) + c | 0;
        a += (b ^ c ^ d) + k[1] - 1530992060 | 0;
        a = (a << 4 | a >>> 28) + b | 0;
        d += (a ^ b ^ c) + k[4] + 1272893353 | 0;
        d = (d << 11 | d >>> 21) + a | 0;
        c += (d ^ a ^ b) + k[7] - 155497632 | 0;
        c = (c << 16 | c >>> 16) + d | 0;
        b += (c ^ d ^ a) + k[10] - 1094730640 | 0;
        b = (b << 23 | b >>> 9) + c | 0;
        a += (b ^ c ^ d) + k[13] + 681279174 | 0;
        a = (a << 4 | a >>> 28) + b | 0;
        d += (a ^ b ^ c) + k[0] - 358537222 | 0;
        d = (d << 11 | d >>> 21) + a | 0;
        c += (d ^ a ^ b) + k[3] - 722521979 | 0;
        c = (c << 16 | c >>> 16) + d | 0;
        b += (c ^ d ^ a) + k[6] + 76029189 | 0;
        b = (b << 23 | b >>> 9) + c | 0;
        a += (b ^ c ^ d) + k[9] - 640364487 | 0;
        a = (a << 4 | a >>> 28) + b | 0;
        d += (a ^ b ^ c) + k[12] - 421815835 | 0;
        d = (d << 11 | d >>> 21) + a | 0;
        c += (d ^ a ^ b) + k[15] + 530742520 | 0;
        c = (c << 16 | c >>> 16) + d | 0;
        b += (c ^ d ^ a) + k[2] - 995338651 | 0;
        b = (b << 23 | b >>> 9) + c | 0;
        // ii()
        a += (c ^ (b | ~d)) + k[0] - 198630844 | 0;
        a = (a << 6 | a >>> 26) + b | 0;
        d += (b ^ (a | ~c)) + k[7] + 1126891415 | 0;
        d = (d << 10 | d >>> 22) + a | 0;
        c += (a ^ (d | ~b)) + k[14] - 1416354905 | 0;
        c = (c << 15 | c >>> 17) + d | 0;
        b += (d ^ (c | ~a)) + k[5] - 57434055 | 0;
        b = (b << 21 | b >>> 11) + c | 0;
        a += (c ^ (b | ~d)) + k[12] + 1700485571 | 0;
        a = (a << 6 | a >>> 26) + b | 0;
        d += (b ^ (a | ~c)) + k[3] - 1894986606 | 0;
        d = (d << 10 | d >>> 22) + a | 0;
        c += (a ^ (d | ~b)) + k[10] - 1051523 | 0;
        c = (c << 15 | c >>> 17) + d | 0;
        b += (d ^ (c | ~a)) + k[1] - 2054922799 | 0;
        b = (b << 21 | b >>> 11) + c | 0;
        a += (c ^ (b | ~d)) + k[8] + 1873313359 | 0;
        a = (a << 6 | a >>> 26) + b | 0;
        d += (b ^ (a | ~c)) + k[15] - 30611744 | 0;
        d = (d << 10 | d >>> 22) + a | 0;
        c += (a ^ (d | ~b)) + k[6] - 1560198380 | 0;
        c = (c << 15 | c >>> 17) + d | 0;
        b += (d ^ (c | ~a)) + k[13] + 1309151649 | 0;
        b = (b << 21 | b >>> 11) + c | 0;
        a += (c ^ (b | ~d)) + k[4] - 145523070 | 0;
        a = (a << 6 | a >>> 26) + b | 0;
        d += (b ^ (a | ~c)) + k[11] - 1120210379 | 0;
        d = (d << 10 | d >>> 22) + a | 0;
        c += (a ^ (d | ~b)) + k[2] + 718787259 | 0;
        c = (c << 15 | c >>> 17) + d | 0;
        b += (d ^ (c | ~a)) + k[9] - 343485551 | 0;
        b = (b << 21 | b >>> 11) + c | 0;
        x[0] = a + x[0] | 0;
        x[1] = b + x[1] | 0;
        x[2] = c + x[2] | 0;
        x[3] = d + x[3] | 0;
    }
    constructor() {
        this._dataLength = 0;
        this._bufferLength = 0;
        this._state = new Int32Array(4);
        this._buffer = new ArrayBuffer(68);
        this._buffer8 = new Uint8Array(this._buffer, 0, 68);
        this._buffer32 = new Uint32Array(this._buffer, 0, 17);
        this.start();
    }
    /**
     * Initialise buffer to be hashed
     */
    start() {
        this._dataLength = 0;
        this._bufferLength = 0;
        this._state.set(Md5.stateIdentity);
        return this;
    }
    // Char to code point to to array conversion:
    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/charCodeAt
    // #Example.3A_Fixing_charCodeAt_to_handle_non-Basic-Multilingual-Plane_characters_if_their_presence_earlier_in_the_string_is_unknown
    /**
     * Append a UTF-8 string to the hash buffer
     * @param str String to append
     */
    appendStr(str) {
        const buf8 = this._buffer8;
        const buf32 = this._buffer32;
        let bufLen = this._bufferLength;
        let code;
        let i;
        for (i = 0; i < str.length; i += 1) {
            code = str.charCodeAt(i);
            if (code < 128) {
                buf8[bufLen++] = code;
            }
            else if (code < 0x800) {
                buf8[bufLen++] = (code >>> 6) + 0xC0;
                buf8[bufLen++] = code & 0x3F | 0x80;
            }
            else if (code < 0xD800 || code > 0xDBFF) {
                buf8[bufLen++] = (code >>> 12) + 0xE0;
                buf8[bufLen++] = (code >>> 6 & 0x3F) | 0x80;
                buf8[bufLen++] = (code & 0x3F) | 0x80;
            }
            else {
                code = ((code - 0xD800) * 0x400) + (str.charCodeAt(++i) - 0xDC00) + 0x10000;
                if (code > 0x10FFFF) {
                    throw new Error('Unicode standard supports code points up to U+10FFFF');
                }
                buf8[bufLen++] = (code >>> 18) + 0xF0;
                buf8[bufLen++] = (code >>> 12 & 0x3F) | 0x80;
                buf8[bufLen++] = (code >>> 6 & 0x3F) | 0x80;
                buf8[bufLen++] = (code & 0x3F) | 0x80;
            }
            if (bufLen >= 64) {
                this._dataLength += 64;
                Md5._md5cycle(this._state, buf32);
                bufLen -= 64;
                buf32[0] = buf32[16];
            }
        }
        this._bufferLength = bufLen;
        return this;
    }
    /**
     * Append an ASCII string to the hash buffer
     * @param str String to append
     */
    appendAsciiStr(str) {
        const buf8 = this._buffer8;
        const buf32 = this._buffer32;
        let bufLen = this._bufferLength;
        let i;
        let j = 0;
        for (;;) {
            i = Math.min(str.length - j, 64 - bufLen);
            while (i--) {
                buf8[bufLen++] = str.charCodeAt(j++);
            }
            if (bufLen < 64) {
                break;
            }
            this._dataLength += 64;
            Md5._md5cycle(this._state, buf32);
            bufLen = 0;
        }
        this._bufferLength = bufLen;
        return this;
    }
    /**
     * Append a byte array to the hash buffer
     * @param input array to append
     */
    appendByteArray(input) {
        const buf8 = this._buffer8;
        const buf32 = this._buffer32;
        let bufLen = this._bufferLength;
        let i;
        let j = 0;
        for (;;) {
            i = Math.min(input.length - j, 64 - bufLen);
            while (i--) {
                buf8[bufLen++] = input[j++];
            }
            if (bufLen < 64) {
                break;
            }
            this._dataLength += 64;
            Md5._md5cycle(this._state, buf32);
            bufLen = 0;
        }
        this._bufferLength = bufLen;
        return this;
    }
    /**
     * Get the state of the hash buffer
     */
    getState() {
        const s = this._state;
        return {
            buffer: String.fromCharCode.apply(null, Array.from(this._buffer8)),
            buflen: this._bufferLength,
            length: this._dataLength,
            state: [s[0], s[1], s[2], s[3]]
        };
    }
    /**
     * Override the current state of the hash buffer
     * @param state New hash buffer state
     */
    setState(state) {
        const buf = state.buffer;
        const x = state.state;
        const s = this._state;
        let i;
        this._dataLength = state.length;
        this._bufferLength = state.buflen;
        s[0] = x[0];
        s[1] = x[1];
        s[2] = x[2];
        s[3] = x[3];
        for (i = 0; i < buf.length; i += 1) {
            this._buffer8[i] = buf.charCodeAt(i);
        }
    }
    /**
     * Hash the current state of the hash buffer and return the result
     * @param raw Whether to return the value as an `Int32Array`
     */
    end(raw = false) {
        const bufLen = this._bufferLength;
        const buf8 = this._buffer8;
        const buf32 = this._buffer32;
        const i = (bufLen >> 2) + 1;
        this._dataLength += bufLen;
        const dataBitsLen = this._dataLength * 8;
        buf8[bufLen] = 0x80;
        buf8[bufLen + 1] = buf8[bufLen + 2] = buf8[bufLen + 3] = 0;
        buf32.set(Md5.buffer32Identity.subarray(i), i);
        if (bufLen > 55) {
            Md5._md5cycle(this._state, buf32);
            buf32.set(Md5.buffer32Identity);
        }
        // Do the final computation based on the tail and length
        // Beware that the final length may not fit in 32 bits so we take care of that
        if (dataBitsLen <= 0xFFFFFFFF) {
            buf32[14] = dataBitsLen;
        }
        else {
            const matches = dataBitsLen.toString(16).match(/(.*?)(.{0,8})$/);
            if (matches === null) {
                return;
            }
            const lo = parseInt(matches[2], 16);
            const hi = parseInt(matches[1], 16) || 0;
            buf32[14] = lo;
            buf32[15] = hi;
        }
        Md5._md5cycle(this._state, buf32);
        return raw ? this._state : Md5._hex(this._state);
    }
}
// Private Static Variables
Md5.stateIdentity = new Int32Array([1732584193, -271733879, -1732584194, 271733878]);
Md5.buffer32Identity = new Int32Array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]);
Md5.hexChars = '0123456789abcdef';
Md5.hexOut = [];
// Permanent instance is to use for one-call hashing
Md5.onePassHasher = new Md5();

class Analytics {
    get config() {
        var _a;
        if (this._config.length === 0) {
            const sdkConfig = getConfig();
            for (const item of sdkConfig.thinking) {
                this._config.push({
                    appId: item.app_id,
                    serverUrl: item.server_url,
                    isErrorTracker: (_a = item.is_error_tracker) !== null && _a !== void 0 ? _a : false,
                    autoTrack: {
                        appShow: true, // 自动采集 ta_mg_show
                        appHide: true // 自动采集 ta_mg_hide
                    }
                });
            }
        }
        return this._config;
    }
    constructor() {
        this._config = [];
        try {
            this.config.forEach((item) => {
                if (item.isErrorTracker) {
                    this._errorTrackerAppId = item.appId;
                }
                TDAnalytics.init(item);
            });
        }
        catch (err) {
            throw new Error(`ThinkingDataAgent Init Error: ${err.message}.` + "且数数app_id, 和server_url为重要配置, 否则数据上报功能不能正常工作。 " +
                "请参考文档: https://topjoy.yuque.com/tsd/zzske8/qn3dgoxtdp0f3x8a");
        }
        this.setSuperProperties({
            "device_id": Device.deviceId,
            "session_id": this.SessionId,
        });
    }
    get SessionId() {
        if (!this._sessionId) {
            this._sessionId = Md5.hashStr(Date.now().toString()).toString();
        }
        return this._sessionId;
    }
    static getInstance() {
        if (!this.instance) {
            this.instance = new Analytics();
        }
        return this.instance;
    }
    setSuperProperties(properties) {
        this.config.forEach((item) => {
            TDAnalytics.setSuperProperties(properties, item.appId);
        });
    }
    userSet(properties) {
        this.config.forEach((item) => {
            TDAnalytics.userSet(properties, item.appId);
        });
    }
    userSetOnce(properties) {
        this.config.forEach((item) => {
            TDAnalytics.userSetOnce(properties, item.appId);
        });
    }
    login(accountId) {
        this.config.forEach((item) => {
            TDAnalytics.login(accountId, item.appId);
        });
    }
    getDistinctId() {
        return TDAnalytics.getDistinctId();
    }
    trackEvent(eventName, properties) {
        const normalInstances = this.config.filter((item) => !item.isErrorTracker);
        console.log("normalInstances数量:", normalInstances.length);
        for (const instance of normalInstances) {
            TDAnalytics.track({ "eventName": eventName, "properties": properties }, instance.appId);
        }
    }
    trackRevenue(eventName, price, currency, properties) {
        const normalInstances = this.config.filter((item) => !item.isErrorTracker);
        for (const instance of normalInstances) {
            properties.set("price", price);
            properties.set("currency", currency);
            TDAnalytics.track({ "eventName": eventName, "properties": properties }, instance.appId);
        }
    }
    trackError(eventName, properties) {
        TDAnalytics.track({ "eventName": eventName, "properties": properties }, this._errorTrackerAppId);
    }
}
// https://docs.thinkingdata.jp/ta-manual/v4.3/installation/installation_menu/client_sdk/game_engine_sdk_installation/cocoscreator_sdk_installation/cocoscreator_sdk_installation.html
// 版本V3.2.0 https://docs-v2.thinkingdata.jp/?version=latest&code=cocoscreator_sdk_installation&anchorId=&lan=zh-CN
Analytics.instance = null;

class Logger {
    // 设置当前日志级别
    static setLogLevel(level) {
        this.currentLevel = level;
    }
    // 调试日志（DEBUG）
    static debug(...args) {
        if (this.currentLevel === 'debug') {
            console.debug('[DEBUG]', ...args);
        }
    }
    // 信息日志（INFO）
    static info(...args) {
        if (['debug', 'info'].indexOf(this.currentLevel) !== -1) {
            console.info('[INFO]', ...args);
        }
    }
    // 警告日志（WARN）
    static warn(...args) {
        if (['debug', 'info', 'warn'].indexOf(this.currentLevel) !== -1) {
            console.warn('[WARN]', ...args);
        }
    }
    // 错误日志（ERROR）
    static error(...args) {
        console.error('[ERROR]', ...args);
    }
}
Logger.currentLevel = 'info'; // 默认日志级别

class GravityEngineAgent {
    static getInstance() {
        if (!this.instance) {
            this.instance = new GravityEngineAgent();
        }
        return this.instance;
    }
    constructor() {
        const accessToken = getModuleConfigValue(Module.GRAVITY, "access_token");
        const name = getModuleConfigValue(Module.GRAVITY, "name");
        let distinctId = '';
        if (accessToken == '' || name == '') {
            throw new TypeError("GravityEngine Init Error, 巨量access_token和name为重要配置, 请确认是否正确配置. 请参考文档: https://topjoy.yuque.com/tsd/zzske8/qn3dgoxtdp0f3x8a");
        }
        try {
            distinctId = Analytics.getInstance().getDistinctId();
        }
        catch (err) {
            throw new Error(`Error: ${err === null || err === void 0 ? void 0 : err.message}` + "ThinkingData GetDistinctId failed. This might indicate ThinkingData initialization failure. Please ensure ThinkingData is properly initialized before starting GravityEngine.");
        }
        const config = {
            clientId: distinctId, // 用户唯一标识，如产品为小游戏，则必须填用户openid（注意，不是小游戏的APPID！！！）
            accessToken: accessToken, // 项目通行证，在：网站后台-->设置-->应用列表中找到Access Token列 复制（首次使用可能需要先新增应用）
            name: name, // 全局变量名称
            debugMode: getModuleConfigValue(Module.GRAVITY, "debug") == true ? "debug" : null, // 是否开启测试模式，开启测试模式后，可以在 网站后台--设置--元数据--事件流中查看实时数据上报结果。（测试时使用，上线之后一定要关掉，改成none或者删除）
            autoTrack: {
                appLaunch: true, // 自动采集 $MPLaunch
                appShow: true, // 自动采集 $MPShow
                appHide: true, // 自动采集 $MPHide
            },
        };
        this.ge = new GravityAnalyticsAPI(config);
        this.ge.setupAndStart();
    }
    setIdentifier(openid, extraParams) {
        /**
         * 在微信登录成功后，第一时间调用该方法，设置openid
         * @param {string} name
         * @param {number} version   用户初始化的程序发布更新的版本号（必填）
         * @param {string} openid    open id (小程序/小游戏必填)
         */
        var _a;
        let version;
        if (extraParams === null) {
            version = 0;
        }
        else {
            version = (_a = extraParams["version"]) !== null && _a !== void 0 ? _a : 0;
        }
        this.ge.initialize({
            name: openid, // 用户名，可以理解成用户在业务中的昵称，如果没有，可以填用户唯一ID（必填）。 静默登录过程中不会要求用户授权昵称。
            version: version,
            openid: openid,
            enable_sync_attribution: false,
        }).then((res) => {
            Logger.info("initialize success " + res);
        }).catch((err) => {
            Logger.info("initialize failed, error is " + err);
        });
        const ta = Analytics.getInstance();
        this.ge.bindTAThirdPlatform(openid, ta.getDistinctId());
    }
    setSuperProperties(properties) {
        this.ge.setSuperProperties(properties);
    }
    trackRegister() {
        this.ge.registerEvent();
    }
    trackEvent(eventName, properties) {
        this.ge.track(eventName, properties);
    }
    trackRevenue(amount, currency, orderId, properties) {
        var _a, _b;
        /**
         * 上报付费事件
         * @param amount     付费金额 单位为分
         * @param currency   货币类型 按照国际标准组织ISO 4217中规范的3位字母，例如CNY人民币、USD美金等
         * @param orderId    订单号 引力引擎会通过订单号 orderId 去重，避免重复上报，请务必传入！
         * @param properties 额外字段 支持的值有 payReason (支付原因), payMethod (支付方式). 可不传.
         */
        let payReason = (_a = properties["payReason"]) !== null && _a !== void 0 ? _a : "";
        let payMethod = (_b = properties["payMethod"]) !== null && _b !== void 0 ? _b : "";
        this.ge.payEvent(amount, currency, orderId, payReason, payMethod);
    }
    trackShowAd(adType, adUnitId, properties) {
        /**
         * 上报广告展示事件 参数如下
         * @param adType           广告类型 （取值为：reward、banner、native、interstitial、video_feed、video_begin，分别对应激励视频广告、Banner广告、原生模板广告、插屏广告、视频广告、视频贴片广告）
         * @param adUnitId         广告位ID（一般以adunit开头，注意不要填错，会导致广告收入统计不准！）
         * @param properties       其他需要携带的自定义参数
         */
        this.ge.adShowEvent(adType, adUnitId, properties);
    }
}
// https://doc.gravity-engine.com/turbo-integrated/mini-program/wechat/mp-sdk.html#_2-2-%E5%85%AC%E5%85%B1%E5%B1%9E%E6%80%A7
GravityEngineAgent.instance = null;

// base64.ts
class Base64 {
    static encode(organic) {
        let encoded = '';
        let chr1, chr2, chr3, enc1, enc2, enc3, enc4;
        let i = 0;
        const _keyStr = this._keyStr;
        organic = this._utf8_encode(organic);
        while (i < organic.length) {
            chr1 = organic.charCodeAt(i++);
            chr2 = organic.charCodeAt(i++);
            chr3 = organic.charCodeAt(i++);
            enc1 = chr1 >> 2;
            enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
            enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
            enc4 = chr3 & 63;
            if (isNaN(chr2)) {
                enc3 = enc4 = 64;
            }
            else if (isNaN(chr3)) {
                enc4 = 64;
            }
            encoded =
                encoded +
                    _keyStr.charAt(enc1) +
                    _keyStr.charAt(enc2) +
                    _keyStr.charAt(enc3) +
                    _keyStr.charAt(enc4);
        }
        return encoded;
    }
    static decode(organic) {
        let output = '';
        let chr1, chr2, chr3;
        let enc1, enc2, enc3, enc4;
        let i = 0;
        const _keyStr = this._keyStr;
        organic = organic.replace(/[^A-Za-z0-9\+\/\=]/g, '');
        while (i < organic.length) {
            enc1 = _keyStr.indexOf(organic.charAt(i++));
            enc2 = _keyStr.indexOf(organic.charAt(i++));
            enc3 = _keyStr.indexOf(organic.charAt(i++));
            enc4 = _keyStr.indexOf(organic.charAt(i++));
            chr1 = (enc1 << 2) | (enc2 >> 4);
            chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
            chr3 = ((enc3 & 3) << 6) | enc4;
            output = output + String.fromCharCode(chr1);
            if (enc3 != 64) {
                output = output + String.fromCharCode(chr2);
            }
            if (enc4 != 64) {
                output = output + String.fromCharCode(chr3);
            }
        }
        output = this._utf8_decode(output);
        return output;
    }
    static _utf8_encode(organic) {
        organic = organic.replace(/\r\n/g, '\n');
        let encoded = '';
        for (let n = 0; n < organic.length; n++) {
            const c = organic.charCodeAt(n);
            if (c < 128) {
                encoded += String.fromCharCode(c);
            }
            else if (c > 127 && c < 2048) {
                encoded += String.fromCharCode((c >> 6) | 192);
                encoded += String.fromCharCode((c & 63) | 128);
            }
            else {
                encoded += String.fromCharCode((c >> 12) | 224);
                encoded += String.fromCharCode(((c >> 6) & 63) | 128);
                encoded += String.fromCharCode((c & 63) | 128);
            }
        }
        return encoded;
    }
    static _utf8_decode(encoded) {
        let string = '';
        let i = 0;
        let c = 0, c2 = 0, c3 = 0;
        while (i < encoded.length) {
            c = encoded.charCodeAt(i);
            if (c < 128) {
                string += String.fromCharCode(c);
                i++;
            }
            else if (c > 191 && c < 224) {
                c2 = encoded.charCodeAt(i + 1);
                string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
                i += 2;
            }
            else {
                c2 = encoded.charCodeAt(i + 1);
                c3 = encoded.charCodeAt(i + 2);
                string += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
                i += 3;
            }
        }
        return string;
    }
}
Base64._keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';

class Status {
    constructor(code, message) {
        this.Code = code;
        this.Message = message;
    }
    jsonWithMsg(msg) {
        return {
            code: this.Code,
            msg: msg ? msg : this.Message
        };
    }
}

class StatusCodeUtils {
    static FromCode(code, type) {
        if (this.statusMapping.has(code)) {
            return this.statusMapping.get(code); // 非空断言运算符
        }
        for (const key of Object.keys(type)) {
            const status = type[key];
            if (status instanceof Status && status.Code === code) {
                this.statusMapping.set(code, status);
                return status;
            }
        }
        return BaseCode.ZeusSDKFail;
    }
}
// 使用 Map 实现类型安全的缓存机制
StatusCodeUtils.statusMapping = new Map();

class BaseCode {
}
BaseCode.Success = new Status(1, "Success");
BaseCode.Cancel = new Status(2, "User Cancelled");
BaseCode.NetworkError = new Status(3, "Network Request Error");
BaseCode.UnknownHostError = new Status(4, "No Address Associated With Host");
BaseCode.ParamError = new Status(5, "Parameter Incorrect");
BaseCode.DataParseError = new Status(6, "Data Parse Error");
BaseCode.SignError = new Status(7, "Sign Error");
BaseCode.UserNotLogin = new Status(8, "User Not Login Yet");
BaseCode.SDKNotInit = new Status(9, "SDK NOT INIT");
BaseCode.ZeusServiceFail = new Status(10, "Fail From Server");
BaseCode.ThirdSDKReturnErrorCode = new Status(11, "Third SDK Returns Error");
BaseCode.ZeusSDKFail = new Status(12, "Zeus SDK Fail");
BaseCode.AntiDupClick = new Status(13, "Could not perform the action because it has been performed recently");
class Base {
    parse(result) {
        var _a;
        this.Status = StatusCodeUtils.FromCode(result.code, BaseCode);
        const message = (_a = result["msg"]) !== null && _a !== void 0 ? _a : "";
        if (message) {
            this.Status.Message = message;
        }
        this.IsSucceeded = this.Status.Code == BaseCode.Success.Code;
    }
}

class LocalData {
    // 保存数据到本地存储
    static save(key, value) {
        sys.localStorage.setItem(key, value);
    }
    // 从本地存储获取数据
    static get(key) {
        return sys.localStorage.getItem(key) || "";
    }
}

class AccountConstants {
}
AccountConstants.LOCAL_ACCOUNT_KEY = "zeus_account";
AccountConstants.LOCAL_ID_KEY = "zeus_userId";
class BIEvent {
}
BIEvent.REGISTER_EVENT = "RoleRegister";
BIEvent.LOGIN_EVENT = "RoleLogin";
BIEvent.BATTLE_EVENT = "BattleFlow";
BIEvent.AD_EVENT = "AdPointFlow";
BIEvent.GUIDE_EVENT = "ClientGuideStep";
class AdFlow {
}
AdFlow.AD_CLICKED = 1;
AdFlow.AD_SHOWED = 2;
AdFlow.AD_COMPLETE = 3;
var LoginType;
(function (LoginType) {
    LoginType[LoginType["Wechat"] = 9] = "Wechat";
    LoginType[LoginType["DouYin"] = 13] = "DouYin";
})(LoginType || (LoginType = {}));

// 定义一个全局变量容器
class GlobalContainer {
    // 确保构造函数是私有的，以实现单例模式
    constructor() {
        this.WechatADConfigKey = 'WechatADConfig';
        this.DouYinADConfigKey = 'DouYinADConfig';
        this.AppModelKey = 'AppModel';
    }
    // 获取单例实例
    static getInstance() {
        if (!GlobalContainer.instance) {
            GlobalContainer.instance = new GlobalContainer();
        }
        return GlobalContainer.instance;
    }
    setWechatADConfig(config) {
        this.set(this.WechatADConfigKey, config);
    }
    getWechatADConfig() {
        return this.get(this.WechatADConfigKey);
    }
    setDouYinADConfig(config) {
        this.set(this.DouYinADConfigKey, config);
    }
    getDouYinADConfig() {
        return this.get(this.DouYinADConfigKey);
    }
    setAppModel(appModel) {
        this.set(this.AppModelKey, appModel);
    }
    getAppModel() {
        return this.get(this.AppModelKey);
    }
    // 存储全局变量
    set(key, value) {
        globalThis[key] = value;
    }
    // 获取全局变量
    get(key) {
        return globalThis[key];
    }
    // 删除全局变量
    delete(key) {
        delete globalThis[key];
    }
}

class ZeusServer {
    static getFingerprint(params) {
        let keys = Object.keys(params);
        keys.sort();
        let sign = '';
        for (let key of keys) {
            let value = params[key];
            if (typeof value == "object") {
                sign += (key + JSON.stringify(value));
            }
            else {
                sign += (key + value.toString());
            }
        }
        const salt = this.getAppModel().AppKey.toString();
        sign += salt;
        return Md5.hashStr(sign).toString();
    }
    static getBillingQrcodeImage(module_name) {
        return getModuleConfigValue(module_name, "billing_qrcode_image_url").toString();
    }
    static getBillingEnv(module_name) {
        return getModuleConfigValue(module_name, "billing_env").toString();
    }
    static getAppModel() {
        const globalContainer = GlobalContainer.getInstance();
        return globalContainer.getAppModel();
    }
    static sendRequest(sender, params, sub_path, success, fail) {
        {
            let serverUrl = ZeusServer.getAppModel().AppRequestURL + sub_path;
            params['sign'] = ZeusServer.getFingerprint(params);
            let paramStr = JSON.stringify(params);
            let base64Str = Base64.encode(paramStr);
            sender.request({
                url: serverUrl,
                data: base64Str,
                header: {
                    "content-type": "application/x-www-form-urlencoded"
                },
                method: 'POST',
                success: function (res) {
                    Logger.info("request to backend server successfully:", res);
                    success(res);
                },
                fail: function (res) {
                    Logger.info("request to backend server failed:", res);
                    fail(res);
                }
            });
        }
    }
    static normalizeZeusServerResponse(resp) {
        if (resp.statusCode != 200) {
            return {
                code: BaseCode.ZeusSDKFail.Code,
                msg: `${resp.errMsg}`
            };
        }
        if (resp.data.error_no != 0) {
            return {
                code: BaseCode.ZeusSDKFail.Code,
                msg: `${resp.data.message}`
            };
        }
        return {
            code: BaseCode.Success.Code,
            msg: `${resp.data.message}`,
            data: resp.data.result
        };
    }
    static verifyLoginStatus(sender, account, success, fail) {
        const params = {
            appid: ZeusServer.getAppModel().AppId,
            device: Device.deviceId,
            account: account,
        };
        const subPath = "/minigame/user/login";
        ZeusServer.sendRequest(sender, params, subPath, success, fail);
    }
    static login(sender, params, success, fail) {
        const _params = {
            appid: ZeusServer.getAppModel().AppId,
            device: Device.deviceId,
            type: params.loginType,
            code: params.code,
            platform: params.platform,
            mobile_info: JSON.stringify(params.mobileInfo)
        };
        const subPath = "/minigame/user/third-login";
        ZeusServer.sendRequest(sender, _params, subPath, success, fail);
    }
    static billing(sender, sendName, params, success, fail) {
        let _params = {
            appid: ZeusServer.getAppModel().AppId,
            user_id: LocalData.get(AccountConstants.LOCAL_ID_KEY),
            product_id: params.productId,
            role_id: params.roleId,
            role_name: params.roleName,
            price: params.price.toString(),
            pay_type: params.billingType,
            pay_notify_url: params.billingCallbackUrl,
            platform: params.platform,
            env: params.billingEnv.toString(),
        };
        let subPath = "";
        if (sendName == "wechat") {
            subPath = "/minigame/order/wechat-exchange";
        }
        else if (sendName == "douyin") {
            subPath = "/minigame/order/douyin-exchange";
        }
        ZeusServer.sendRequest(sender, _params, subPath, success, fail);
    }
    static sendSubscribeMessage(sender, sendName, openId, tmplId, success, fail) {
        let _params = {
            appid: ZeusServer.getAppModel().AppId,
            touser: openId,
            template_id: tmplId,
            page: 'index',
            miniprogram_state: 'developer',
            lang: 'zh_CN',
            data: {
                thing04: {
                    value: 'test_role_wy'
                },
                thing02: {
                    value: '测试内容'
                }
            }
        };
        let subPath = `/minigame/${sendName}/message/subscribe/send`;
        ZeusServer.sendRequest(sender, _params, subPath, success, fail);
    }
    static gameInfo(sender, success, fail) {
        const _params = {
            appid: ZeusServer.getAppModel().AppId,
            lang: "1"
        };
        const subPath = "/game/info";
        ZeusServer.sendRequest(sender, _params, subPath, success, fail);
    }
}

class LoginCode extends BaseCode {
}
LoginCode.UserNotExist = new Status(2001, "User not exist");
LoginCode.RegisterFail = new Status(2002, "Register fail");
LoginCode.DeviceIsEmulator = new Status(2003, "Device is emulator");
LoginCode.PassUnqualify = new Status(2004, "Mobile phone number or Email is invalid");
LoginCode.PasswordUnqualify = new Status(2005, "Password is invalid");
LoginCode.StatusFailByService = new Status(2006, "service return login fail.");
LoginCode.PrefetchNumberFail = new Status(2007, "Prefetch number failed.");
class LoginDetail {
}
class Login extends Base {
    constructor() {
        super(...arguments);
        this.Detail = new LoginDetail();
    }
    parse(resp) {
        var _a, _b, _c, _d, _e;
        super.parse(resp);
        this.Status = StatusCodeUtils.FromCode(resp.code, LoginCode);
        if (!resp.data) {
            return;
        }
        this.ID = resp.data.id;
        this.Account = resp.data.account;
        this.Token = resp.data.token;
        this.LoginType = LoginType[resp.data.login_type];
        this.IsFirstRegister = (_a = resp.data.first_register) !== null && _a !== void 0 ? _a : false;
        this.Detail.openid = (_b = resp.data.detail) === null || _b === void 0 ? void 0 : _b.openid;
        this.Detail.session_key = (_c = resp.data.detail) === null || _c === void 0 ? void 0 : _c.session_key;
        this.Detail.unionid = (_d = resp.data.detail) === null || _d === void 0 ? void 0 : _d.unionid;
        this.IsSkipAd = (_e = resp.data.skip_ad) !== null && _e !== void 0 ? _e : false;
    }
}
// 成功：
// {
//     "error_no": 0,
//     "message": "成功",
//     "result": {
//         "id": 73832,
//         "account": *********,
//         "token": "iZLHBarVCjcBknYgeoVkvFljBJqfiULE",
//         "is_real_name_authentication": true,
//         "adult": false,
//         "trace_id": "593e0403868d4b8a997650dffe7139e4"
//         "detail": {
//             "openid": "_000B|soKQsnpla6rYlja3nMMly591FVJA4n"
//             "session_key": "TJsT+uZJoUDaf9u7mrURuQ==",
//             "unionid": "48a76159-473e-5ead-8948-712be28e62a3"
//         ｝
//     },
//     "trace_id": "593e0403868d4b8a997650dffe7139e4",
//     "code": 1,
//     "yk_login": true,
//     "reStartLogin": 0,
//     "status": 0,
//     "logintype": 0
// }
//
// 失败：
// {
//     "code": 4,
//     "msg": "com.topjoy.zeussdk.utils.MCNetUtil$OkHttpDns@5109c36 returned no addresses for zeus.youle.game",
//     "net_name": "login",
//     "status": 0,
//     "third": 0,
//     "type": 0
// }

class BillingCode extends BaseCode {
}
BillingCode.ProductTypeError = new Status(3001, "Product type error");
BillingCode.BillingServiceDisconnected = new Status(3002, "Billing service disconnected");
BillingCode.DataParseError2 = new Status(3003, "Data parse error");
BillingCode.OrderInfoEmpty = new Status(3004, "Order info empty");
BillingCode.CreateOrderFail = new Status(3005, "Create order fail");
BillingCode.NotSupportSubscription = new Status(3006, "Not support subscription");
BillingCode.BillingClientNotReady = new Status(3007, "BillingClient not ready");
BillingCode.NotQuerySKUDetail = new Status(3008, "Not query sku detail");
BillingCode.StartPayFail = new Status(3009, "Start pay fail");
BillingCode.PayVerifyFail = new Status(3010, "Pay verify fail");
BillingCode.PaySuccessConsumeFail = new Status(3011, "Pay success, consume fail");
BillingCode.SystemNotSupport = new Status(3012, "System not support");
BillingCode.JsApiUnknownResult = new Status(3013, "SDK cant know pay result, please handle it."); // 受限于 JsApi 支付无法提供准确的支付结果以及支付是否结束，需要项目组自行处理。

/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
/* global Reflect, Promise, SuppressedError, Symbol, Iterator */


function __rest(s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
}

typeof SuppressedError === "function" ? SuppressedError : function (error, suppressed, message) {
    var e = new Error(message);
    return e.name = "SuppressedError", e.error = error, e.suppressed = suppressed, e;
};

class Notification extends Base {
    constructor() {
        super(...arguments);
        this.TopicResult = {};
        this.IsSucceeded = false;
    }
    parse(resp) {
        super.parse(resp);
        this.Status = StatusCodeUtils.FromCode(resp.code !== undefined ? resp.code : BaseCode.Success.Code, BaseCode);
        if (resp.errMsg === "requestSubscribeMessage:ok") {
            // 复制所有属性，除了 errMsg
            const { errMsg } = resp, topicResults = __rest(resp, ["errMsg"]);
            this.TopicResult = topicResults;
        }
        else if (resp.topicResult) {
            this.TopicResult = resp.topicResult;
        }
        this.IsSucceeded = this.Status.Code === BaseCode.Success.Code;
    }
}

class Network extends Base {
    parse(resp) {
        super.parse(resp);
        this.NetworkType = resp.network;
    }
}

class Questionnaire extends Base {
    parse(resp) {
        var _a;
        super.parse(resp);
        this.Status = StatusCodeUtils.FromCode(resp.code, BaseCode);
        this.SurveyCode = (_a = resp.questionnaire_code) !== null && _a !== void 0 ? _a : "";
        this.IsFinished = resp.code === BaseCode.Success.Code;
    }
}

class ZeusSDKVersion {
}
ZeusSDKVersion.SDKVersion = "3.0.18.3-SNAPSHOT";

class SDKUserInfo {
    constructor() {
        this.ID = "";
        this.Name = "";
        this.Email = "";
        this.Picture = "";
        this.Accounts = [];
    }
}
class GetUserInfo extends Base {
    constructor() {
        super();
        this.UserInfo = new SDKUserInfo();
        this.Friends = [];
    }
    parse(result) {
        // 首先解析基础状态信息
        super.parse(result);
        // 如果成功且有用户信息
        if (result.errMsg === "getUserInfo:ok" && result.userInfo) {
            this.parseUserInfo(this.UserInfo, {
                id: "", // 微信getUserInfo不返回id
                name: result.userInfo.nickName,
                email: "", // 微信getUserInfo不返回email
                avatar: result.userInfo.avatarUrl,
                accounts: [] // 微信getUserInfo不返回accounts
            });
        }
        if (this.IsSucceeded && result.data) {
            // 解析用户信息
            if (result.data.UserInfo) {
                this.parseUserInfo(this.UserInfo, result.data.UserInfo);
            }
            // 解析好友列表
            if (Array.isArray(result.data.Friends)) {
                this.Friends = result.data.Friends.map(friendData => {
                    const friend = new SDKUserInfo();
                    this.parseUserInfo(friend, friendData);
                    return friend;
                });
            }
        }
    }
    parseUserInfo(info, data) {
        var _a, _b;
        info.ID = data.id || "";
        info.Name = data.name || "";
        info.Email = data.email || "";
        info.Picture = data.avatar || ((_b = (_a = data.picture) === null || _a === void 0 ? void 0 : _a.data) === null || _b === void 0 ? void 0 : _b.url) || "";
        info.Accounts = Array.isArray(data.accounts) ? data.accounts : [];
    }
}

class ZeusTracker {
    constructor() {
        this.THINKING_APPID = "226d1c95988343a78f35131292ef56af";
        this.THINKING_URL = "https://oversea-log.topjoy.com";
        // 私有构造函数，防止外部创建实例
    }
    static getInstance() {
        if (!ZeusTracker.instance) {
            ZeusTracker.instance = new ZeusTracker();
        }
        return ZeusTracker.instance;
    }
    init() {
        TDAnalytics.init({
            serverUrl: this.THINKING_URL,
            appId: this.THINKING_APPID,
        });
    }
    track(sdkEvent) {
        TDAnalytics.track(sdkEvent.GetJson(), this.THINKING_APPID);
    }
}
ZeusTracker.instance = null;

class BaseAdapter {
}

class InitResult extends Base {
    constructor() {
        super();
        this.IsRechargeable = null;
    }
    parse(resp) {
        super.parse(resp);
        this.Status = StatusCodeUtils.FromCode(resp.code !== undefined ? resp.code : BaseCode.Success.Code, BaseCode);
        this.IsRechargeable = resp["is_recharge_open"] !== undefined ? resp["is_recharge_open"].toString() : null;
    }
}

class AdCode extends BaseCode {
}
AdCode.ADDisplayed = new Status(7001, "AD displayed.");
AdCode.ADDisplayFailed = new Status(7002, "AD display failed.");
AdCode.ADClicked = new Status(7003, "AD clicked.");
AdCode.ADHidden = new Status(7004, "AD hidden.");
AdCode.ADLoaded = new Status(7005, "AD loaded.");
AdCode.ADIsNotReady = new Status(7006, "Ad is loading or loaded fail.");
AdCode.ADUserRewarded = new Status(7007, "User rewarded.");
AdCode.ADRevenuePaid = new Status(7008, "AD revenue paid.");
AdCode.ADSDKNotInit = new Status(7009, "AD SDK not initialized.");
class ADResult {
    constructor() {
        this.adUnitId = null;
        this.networkName = null;
        this.networkPlacement = null;
        this.placement = null;
        this.requestLatencyMillis = 0;
        this.creativeId = null;
        this.adReviewCreativeId = null;
        this.revenue = 0.0;
        this.revenuePrecision = null;
        this.dspName = null;
        this.dspId = null;
    }
}
class Ad extends Base {
    constructor() {
        super(...arguments);
        this.ADResult = new ADResult();
        this.IsSucceeded = true;
        this.Uuid = null;
        this.Type = null;
        this.Platform = null;
    }
    parse(resp) {
        super.parse(resp);
        this.Status = StatusCodeUtils.FromCode(resp.code, AdCode);
        // 如果resp中包含msg，则更新Status.Message
        if (resp.msg) {
            this.Status.Message = resp.msg;
        }
        this.IsSucceeded = this.Status.Code === AdCode.Success.Code ||
            this.Status.Code === AdCode.ADDisplayed.Code ||
            this.Status.Code === AdCode.ADUserRewarded.Code ||
            this.Status.Code === AdCode.ADRevenuePaid.Code ||
            this.Status.Code === AdCode.ADClicked.Code ||
            this.Status.Code === AdCode.ADHidden.Code;
        this.Uuid = resp.uuid;
        this.Type = resp.type;
        this.Platform = resp.platform;
    }
}

class UUID {
    static generate() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
            const r = (Math.random() * 16) | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
}

const globalContainer$2 = GlobalContainer.getInstance();
class AdManager {
    constructor(media, media_type) {
        var _a, _b, _c, _d;
        this.rewardedVideoAd = null;
        this.interstitialAd = null;
        this._type = null;
        this._uuid = null;
        this._callback = null;
        this.media = media;
        this.mediaType = media_type;
        if (media_type === AdMedia.WECHAT) {
            this.rewardAdUnitId = (_a = globalContainer$2.getWechatADConfig()) === null || _a === void 0 ? void 0 : _a.rewardAdUnitId;
            this.interstitialAdUnitId = (_b = globalContainer$2.getWechatADConfig()) === null || _b === void 0 ? void 0 : _b.interstitialAdUnitId;
        }
        else if (media_type === AdMedia.BYTE_DANCE) {
            this.rewardAdUnitId = (_c = globalContainer$2.getDouYinADConfig()) === null || _c === void 0 ? void 0 : _c.rewardAdUnitId;
            this.interstitialAdUnitId = (_d = globalContainer$2.getDouYinADConfig()) === null || _d === void 0 ? void 0 : _d.interstitialAdUnitId;
        }
        else {
            Logger.error(`unknown media type: ${media_type}`);
        }
        if (!this.rewardAdUnitId || !this.interstitialAdUnitId) {
            Logger.error(`could not find ${this.mediaType} ad unit id set`);
        }
        this.init();
    }
    ;
    // 初始化广告实例
    init() {
        this.initRewardedVideoAd();
        this.initInterstitialAd();
    }
    ;
    initRewardedVideoAd() {
        console.log("initRewardedVideoAd 激励广告初始化");
        if (!this.media.createRewardedVideoAd) {
            Logger.error('当前基础库不支持激励视频广告');
            return;
        }
        this.rewardedVideoAd = this.media.createRewardedVideoAd({
            adUnitId: this.rewardAdUnitId
        });
        this.rewardedVideoAd.onLoad(() => {
            Logger.debug("rewarded ad loaded successfully");
        });
        this.rewardedVideoAd.onError((err) => {
            Logger.debug(`rewarded ad loaded failed: ${err}`);
        });
    }
    ;
    initInterstitialAd() {
        console.log("initInterstitialAd 插屏广告初始化");
        this.interstitialAd = this.media.createInterstitialAd({
            adUnitId: this.interstitialAdUnitId
        });
        this.interstitialAd.onLoad(() => {
            Logger.debug("interstitial ad loaded successfully");
        });
        this.interstitialAd.onError((err) => {
            Logger.debug(`interstitial ad loaded failed: ${err}`);
        });
    }
    ;
    showRewardedAd(placement, callback) {
        this._uuid = UUID.generate();
        const properties = {
            ad_placement: placement,
            ad_type: AdType.REWARD,
            ad_media: this.mediaType,
            ad_step: AdFlow.AD_CLICKED,
            ad_unit_id: this.rewardAdUnitId,
            ad_session_id: this._uuid,
        };
        Analytics.getInstance().trackEvent(BIEvent.AD_EVENT, properties);
        if (this.rewardedVideoAd == null) {
            Logger.warn("rewardedAd is not ready");
            return;
        }
        // 保存回调和相关信息
        this._type = AdType.REWARD;
        this._callback = callback;
        // 添加回调处理标志，防止多次回调
        let callbackHandled = false;
        // 统一的成功回调处理
        const handleSuccess = () => {
            if (callbackHandled)
                return;
            callbackHandled = true;
            properties.ad_step = AdFlow.AD_COMPLETE;
            Analytics.getInstance().trackEvent(BIEvent.AD_EVENT, properties);
            // 正常播放结束，可以下发游戏奖励
            this.complete(this.rewardAdUnitId, AdCode.Success.jsonWithMsg(""));
            this.complete(this.rewardAdUnitId, AdCode.ADHidden.jsonWithMsg(""));
            this.complete(this.rewardAdUnitId, AdCode.ADUserRewarded.jsonWithMsg(""));
        };
        // 统一的取消回调处理
        const handleCancel = () => {
            if (callbackHandled)
                return;
            callbackHandled = true;
            this.cancel(this.rewardAdUnitId);
        };
        // 先移除之前的事件监听器，避免重复绑定
        this.rewardedVideoAd.offClose();
        this.rewardedVideoAd.offError();
        // 监听广告关闭事件
        this.rewardedVideoAd.onClose(res => {
            Logger.debug(`RewardedClose 广告关闭 ${res.isEnded} | ${res.errMsg}`);
            GravityEngineAgent.getInstance().trackShowAd(AdType.REWARD, this.rewardAdUnitId, null);
            if (res && res.isEnded) {
                handleSuccess();
            }
            else {
                handleCancel();
            }
        });
        // 监听广告错误事件
        this.rewardedVideoAd.onError((err) => {
            Logger.debug(`RewardedError 广告错误 ${err.errCode} | ${err.errMsg}`);
            if (callbackHandled)
                return;
            callbackHandled = true;
            this.complete(this.rewardAdUnitId, AdCode.ADDisplayFailed.jsonWithMsg(err.errMsg));
        });
        this.showRewardedVideoAdWithRetry(properties, callbackHandled);
    }
    ;
    showRewardedVideoAdWithRetry(properties, callbackHandled) {
        return this.rewardedVideoAd.show()
            .then(() => {
            console.log('激励视频 广告显示');
            Logger.debug("rewardedVideoAd.show========");
            properties.ad_step = AdFlow.AD_SHOWED;
            if (!callbackHandled) {
                this.complete(this.rewardAdUnitId, AdCode.ADDisplayed.jsonWithMsg(""));
            }
            Analytics.getInstance().trackEvent(BIEvent.AD_EVENT, properties);
        })
            .catch(() => {
            // 失败重试，主动load一下广告
            return this.rewardedVideoAd.load()
                .then(() => {
                return this.rewardedVideoAd.show();
            })
                .then(() => {
                console.log('激励视频 广告显示（重试成功）');
                Logger.debug("rewardedVideoAd.show retry complete");
                properties.ad_step = AdFlow.AD_SHOWED;
                if (!callbackHandled) {
                    this.complete(this.rewardAdUnitId, AdCode.ADDisplayed.jsonWithMsg(""));
                }
                Analytics.getInstance().trackEvent(BIEvent.AD_EVENT, properties);
            })
                .catch((err) => {
                if (callbackHandled)
                    return;
                callbackHandled = true;
                let errMsg = '';
                if (typeof err === 'string') {
                    errMsg = err;
                }
                else if (err && typeof err.errMsg === 'string') {
                    errMsg = err.errMsg;
                }
                else if (err && err.toString) {
                    errMsg = err.toString();
                }
                this.complete(this.rewardAdUnitId, AdCode.ADIsNotReady.jsonWithMsg(errMsg));
            });
        });
    }
    ;
    showInterstitialAd(placement, callback) {
        this._uuid = UUID.generate();
        const properties = {
            ad_placement: placement,
            ad_type: AdType.INTERSTITIAL,
            ad_media: this.mediaType,
            ad_step: AdFlow.AD_CLICKED,
            ad_unit_id: this.interstitialAdUnitId,
            ad_session_id: this._uuid,
        };
        Analytics.getInstance().trackEvent(BIEvent.AD_EVENT, properties);
        if (this.interstitialAd == null) {
            this.initInterstitialAd();
        }
        // 保存回调和相关信息
        this._type = AdType.INTERSTITIAL;
        this._callback = callback;
        // 添加错误处理标志
        let errorHandled = false;
        // 统一的错误处理函数
        const handleError = (err) => {
            if (errorHandled)
                return; // 如果已经处理过错误，直接返回
            errorHandled = true;
            // 安全地获取错误消息
            let errMsg = '';
            if (typeof err === 'string') {
                errMsg = err;
            }
            else if (err && typeof err.errMsg === 'string') {
                errMsg = err.errMsg;
            }
            else if (err && err.toString) {
                errMsg = err.toString();
            }
            console.log(`InterstitialError 广告错误 ${err.errCode || ''} | ${errMsg}`);
            this.complete(this.interstitialAdUnitId, AdCode.ADDisplayFailed.jsonWithMsg(errMsg));
        };
        // 先移除之前的事件监听器，避免重复绑定
        this.interstitialAd.offClose();
        this.interstitialAd.offError();
        // 监听广告关闭事件
        this.interstitialAd.onClose(res => {
            Logger.debug("InterstitialClose 广告关闭");
            console.log("InterstitialClose 广告关闭");
            GravityEngineAgent.getInstance().trackShowAd(AdType.INTERSTITIAL, this.interstitialAdUnitId, null);
            properties.ad_step = AdFlow.AD_COMPLETE;
            Analytics.getInstance().trackEvent(BIEvent.AD_EVENT, properties);
            // 正常播放结束，可以下发游戏奖励
            this.complete(this.interstitialAdUnitId, AdCode.ADDisplayed.jsonWithMsg(""));
            this.complete(this.interstitialAdUnitId, AdCode.Success.jsonWithMsg(""));
            this.complete(this.interstitialAdUnitId, AdCode.ADHidden.jsonWithMsg(""));
            this.complete(this.interstitialAdUnitId, AdCode.ADUserRewarded.jsonWithMsg(""));
            // 只在抖音平台执行销毁操作，微信不需要销毁
            if (this.mediaType === AdMedia.BYTE_DANCE) {
                this.interstitialAd.destroy();
                this.interstitialAd = null;
            }
        });
        // 监听广告错误事件
        this.interstitialAd.onError(handleError);
        this.interstitialAd.show()
            .then(() => {
            console.log('插屏广告显示');
            properties.ad_step = AdFlow.AD_SHOWED;
            Analytics.getInstance().trackEvent(BIEvent.AD_EVENT, properties);
        })
            .catch(handleError); // 使用相同的错误处理函数
    }
    ;
    // 回调
    complete(adUnitId, resp) {
        resp.uuid = this._uuid;
        resp.type = this._type;
        resp.platform = this.mediaType;
        let adResult = new Ad();
        adResult.parse(resp);
        adResult.ADResult.adUnitId = adUnitId;
        this._callback(adResult);
    }
    ;
    // 取消回调
    cancel(adUnitId) {
        let json = AdCode.Cancel.jsonWithMsg(AdCode.Cancel.Message);
        json["uuid"] = this._uuid;
        json["type"] = this._type;
        json["platform"] = this.mediaType;
        let adResult = new Ad();
        adResult.parse(json);
        adResult.ADResult.adUnitId = adUnitId;
        this._callback(adResult);
    }
    ;
}

class SkipAdManager extends AdManager {
    constructor(media, media_type) {
        super(media, media_type);
    }
    ;
    showRewardedAd(placement, callback) {
        this._type = AdType.REWARD;
        this._callback = callback;
        this.complete('', AdCode.Success.jsonWithMsg(""));
    }
    ;
    showInterstitialAd(placement, callback) {
        this._type = AdType.INTERSTITIAL;
        this._callback = callback;
        this.complete('', AdCode.Success.jsonWithMsg(""));
    }
    ;
    complete(adUnitId, resp) {
        resp['uuid'] = Date.now().toString();
        resp['type'] = this._type;
        resp['platform'] = this.mediaType;
        let adResult = new Ad();
        adResult.parse(resp);
        adResult.ADResult.adUnitId = '';
        this._callback(adResult);
    }
    ;
}

function getAdManager(skipAd, platform, mediaType) {
    if (skipAd) {
        return new SkipAdManager(platform, mediaType);
    }
    else {
        return new AdManager(platform, mediaType);
    }
}

var BillingType$1;
(function (BillingType) {
    BillingType["IOS"] = "jsapi";
    BillingType["ANDROID"] = "virtual-payment";
    BillingType["UNKNOWN"] = "unknown";
})(BillingType$1 || (BillingType$1 = {}));
const globalContainer$1 = GlobalContainer.getInstance();
class RankOption {
    constructor(dict) {
        this.type = 'engine';
        this.event = 'viewport';
        this.rankType = dict.rankType;
        this.shareTicket = dict.shareTicket || '';
        this.x = dict.x || 0;
        this.y = dict.y || 0;
        this.width = dict.width || 0;
        this.height = dict.height || 0;
    }
}
class WeChatAdapter extends BaseAdapter {
    constructor() {
        super();
        this.loginType = "9";
        this._analytics = null;
        this.source_name = "wechat";
        this.wx = globalThis.wx;
        this._analytics = Analytics.getInstance();
        this._analytics.setSuperProperties({ "source_name": this.source_name });
    }
    get deviceInfo() {
        if (!this._deviceInfo) {
            this._deviceInfo = this.wx.getDeviceInfo();
        }
        return this._deviceInfo;
    }
    get platform() {
        return this.deviceInfo.platform;
    }
    static getInstance() {
        if (!this.instance) {
            this.instance = new WeChatAdapter();
        }
        return this.instance;
    }
    init(callback) {
        ZeusServer.gameInfo(this.wx, (res) => {
            var _a;
            Logger.debug("get game info successfully: ", res);
            const respResult = (_a = res.data) === null || _a === void 0 ? void 0 : _a.result;
            if (respResult) {
                if (respResult['wechatAdConfig']) {
                    globalContainer$1.setWechatADConfig({
                        rewardAdUnitId: respResult['wechatAdConfig']['rewarded_ad_id'],
                        interstitialAdUnitId: respResult['wechatAdConfig']['interstitial_ad_id']
                    });
                }
            }
            respResult['code'] = BaseCode.Success.Code;
            this._isInit = true;
            this._adManager = getAdManager(false, this.wx, AdMedia.WECHAT);
            const tracker = ZeusTracker.getInstance();
            tracker.init();
            callback(respResult);
        }, (err) => {
            Logger.debug("get game info failed: ", err);
            let baserResult = new Base();
            const jsonData = BaseCode.ZeusServiceFail.jsonWithMsg("");
            baserResult.parse(jsonData);
            callback(baserResult);
        });
    }
    login(callback) {
        const self = this;
        if (!self._isInit) {
            let result = new InitResult();
            result.parse(BaseCode.SDKNotInit.jsonWithMsg(''));
            callback(result);
            return;
        }
        Logger.debug("check wechat session status");
        this.wx.checkSession({
            success: function (res) {
                let account = LocalData.get(AccountConstants.LOCAL_ACCOUNT_KEY);
                Logger.debug("trying to get account from local storage: ", account, "result: ", res);
                if (account) {
                    Logger.debug("found account in local storage: ", account, " start verify login status with Zeus Server");
                    let loginResult = new Login();
                    ZeusServer.verifyLoginStatus(self.wx, account, function (res) {
                        var _a;
                        if (res && ((_a = res === null || res === void 0 ? void 0 : res.data) === null || _a === void 0 ? void 0 : _a.result)) {
                            res.data.result["login_type"] = LoginType.Wechat;
                            Logger.debug("verify login status successfully: ", res);
                            loginResult.parse(ZeusServer.normalizeZeusServerResponse(res));
                            self.loginSuccess(loginResult);
                            self._analytics.setSuperProperties({
                                "open_id": loginResult.Detail.openid,
                                "zeus_open_id": loginResult.Account,
                                "zeus_login_way": loginResult.LoginType,
                                "zeus_version": ZeusSDKVersion.SDKVersion,
                            });
                            self._analytics.login(account);
                            self._analytics.trackEvent(BIEvent.LOGIN_EVENT, {});
                            // 在静默登录场景下不会要求用户授权获取用户名称，这里用open id代替。
                            const props = { "user_name": loginResult.Detail.openid };
                            GravityEngineAgent.getInstance().setIdentifier(loginResult.Detail.openid, props);
                            callback(loginResult);
                        }
                        else {
                            Logger.debug("verify login status failed: ", res);
                            self._isLogin = false;
                            loginResult.parse(ZeusServer.normalizeZeusServerResponse(res));
                            callback(loginResult);
                        }
                    }, function (res) {
                        Logger.debug("verify login status failed: ", res);
                        self._isLogin = false;
                        loginResult.parse(ZeusServer.normalizeZeusServerResponse(res));
                        callback(loginResult);
                    });
                }
                else {
                    Logger.debug("no account in local storage: ", account, " start login with wechat");
                    self.doLogin(callback);
                }
            },
            fail: function (res) {
                Logger.debug("wechat session check failed: ", res, " start login with wechat");
                self._isLogin = false;
                self.doLogin(callback);
            }
        });
    }
    doLogin(callback) {
        const self = this;
        let loginResult = new Login();
        this.wx.login({
            success: function (res) {
                const loginParams = {
                    loginType: self.loginType,
                    code: res.code,
                    platform: self.platform,
                    mobileInfo: self.deviceInfo
                };
                Logger.debug("wechat login successfully: ", res, " start login with Zeus Server");
                ZeusServer.login(self.wx, loginParams, function (res) {
                    Logger.debug("Zeus Server login successfully: ", res);
                    res.data.result["login_type"] = LoginType.Wechat;
                    loginResult.parse(ZeusServer.normalizeZeusServerResponse(res));
                    self.loginSuccess(loginResult);
                    self._analytics.login(loginResult.Account);
                    self._analytics.setSuperProperties({
                        "open_id": loginResult.Detail.openid,
                        "zeus_open_id": loginResult.Account,
                        "zeus_login_way": loginResult.LoginType,
                        "zeus_version": ZeusSDKVersion.SDKVersion,
                    });
                    self._analytics.trackEvent(BIEvent.LOGIN_EVENT, {});
                    // 在静默登录场景下不会要求用户授权获取用户名称，这里用open id代替。
                    const props = { "user_name": loginResult.Detail.openid };
                    GravityEngineAgent.getInstance().setIdentifier(loginResult.Detail.openid, props);
                    if (loginResult.IsFirstRegister) {
                        GravityEngineAgent.getInstance().trackRegister();
                    }
                    callback(loginResult);
                }, function (res) {
                    Logger.debug("Zeus Server login failed: ", res);
                    self._isLogin = false;
                    loginResult.parse(ZeusServer.normalizeZeusServerResponse(res));
                    callback(loginResult);
                });
            },
            fail: function (err) {
                Logger.debug("wechat login failed: ", err);
                self._isLogin = false;
                const res = {
                    code: LoginCode.ThirdSDKReturnErrorCode.Code,
                    msg: `with code: ${err.errno} error: ${err.errMsg}`
                };
                loginResult.parse(res);
                callback(loginResult);
            },
            complete: function (res) {
                Logger.info('login complete', res);
            },
        });
    }
    loginSuccess(loginResult) {
        this._isLogin = true;
        const isSkipAd = loginResult.IsSkipAd;
        this._adManager = getAdManager(isSkipAd, this.wx, AdMedia.WECHAT);
        LocalData.save(AccountConstants.LOCAL_ACCOUNT_KEY, loginResult.Account);
        LocalData.save(AccountConstants.LOCAL_ID_KEY, loginResult.ID.toString());
    }
    doGetUserInfo(callback) {
        this.wx.getUserInfo({
            success: (res) => {
                Logger.debug('getUserInfo success', res);
                let userInfoResult = new GetUserInfo();
                const result = {
                    code: BaseCode.Success.Code,
                    msg: res.errMsg,
                    errMsg: res.errMsg,
                    userInfo: res.userInfo
                };
                userInfoResult.parse(result);
                callback(userInfoResult);
            },
            fail: (err) => {
                Logger.debug('getUserInfo failed', err);
                let userInfoResult = new GetUserInfo();
                const result = {
                    code: BaseCode.ThirdSDKReturnErrorCode.Code,
                    msg: err.errMsg,
                    errMsg: err.errMsg
                };
                userInfoResult.parse(result);
                callback(userInfoResult);
            }
        });
    }
    getUserInfo(callback) {
        if (this._isLogin) {
            const systemInfo = this.wx.getSystemInfoSync();
            const screenWidth = systemInfo.screenWidth;
            const screenHeight = systemInfo.screenHeight;
            const buttonStyle = {
                type: 'text',
                text: '',
                style: {
                    left: 0,
                    top: 0,
                    width: screenWidth,
                    height: screenHeight,
                    backgroundColor: 'transparent', // 设置背景透明
                    color: 'transparent', // 设置文本颜色透明（如果有文本）
                    borderColor: 'transparent' // 设置边框颜色透明
                }
            };
            this.wx.getSetting({
                success: (res) => {
                    if (res.authSetting['scope.userInfo'] === true) {
                        this.doGetUserInfo(callback);
                    }
                    else {
                        const button = this.wx.createUserInfoButton(buttonStyle);
                        button.onTap((res) => {
                            if (res.errMsg.indexOf(':ok') > -1 && !!res.rawData) {
                                // 获取用户信息
                                this.doGetUserInfo(callback);
                                button.hide();
                            }
                        });
                    }
                },
                fail: (err) => {
                    let userInfoResult = new Base();
                    userInfoResult.parse(BaseCode.ThirdSDKReturnErrorCode.jsonWithMsg(`err: ${JSON.stringify(err)} `));
                    callback(userInfoResult);
                }
            });
        }
        else {
            let userInfoResult = new Base();
            userInfoResult.parse(BaseCode.UserNotLogin.jsonWithMsg(""));
            callback(userInfoResult);
        }
    }
    get billingType() {
        if (this.platform == "android" || this.platform == "windows") {
            return BillingType$1.ANDROID;
        }
        else if (this.platform == 'ios') {
            return BillingType$1.IOS;
        }
        else {
            return BillingType$1.UNKNOWN;
        }
    }
    normalizeWeChatBillingResponse(resp) {
        let res;
        // 成功的情况下不存在errCode字段
        if (resp.errCode === undefined) {
            res = {
                code: BillingCode.Success.Code,
                msg: `Success: ${resp.errMsg}`
            };
        }
        else {
            switch (resp.errCode) {
                case 0:
                    res = {
                        code: BillingCode.Success.Code,
                        msg: `with code: ${resp.errCode} error: ${resp.errMsg}`
                    };
                    break;
                case 1:
                case -2:
                    res = {
                        code: BillingCode.Cancel.Code,
                        msg: `with code: ${resp.errCode} error: ${resp.errMsg}`
                    };
                    break;
                default:
                    res = {
                        code: BillingCode.ThirdSDKReturnErrorCode.Code,
                        msg: `with code: ${resp.errCode} error: ${resp.errMsg}`
                    };
            }
        }
        return res;
    }
    androidBillingProcess(params, callback) {
        const self = this;
        let billingResult = new Base();
        params["success"] = function (resp) {
            const res = self.normalizeWeChatBillingResponse(resp);
            billingResult.parse(res);
            callback(billingResult);
        };
        params["fail"] = function (resp) {
            const res = self.normalizeWeChatBillingResponse(resp);
            billingResult.parse(res);
            callback(billingResult);
        };
        self.wx.requestMidasPaymentGameItem(params);
    }
    iosBillingProcess(callback) {
        this.wx.openCustomerServiceConversation({
            showMessageCard: true,
            sendMessageImg: ZeusServer.getBillingQrcodeImage(Module.WECHAT),
            sessionFrom: "会话来源，透传后台，byte<1000",
            fail: (err) => {
                let billingResult = new Base();
                billingResult.parse(BillingCode.ThirdSDKReturnErrorCode.jsonWithMsg(""));
                callback(billingResult);
            },
            complete: () => {
                let billingResult = new Base();
                billingResult.parse(BillingCode.JsApiUnknownResult.jsonWithMsg(""));
                callback(billingResult);
            }
        });
    }
    unsupportedBillingProcess(callback) {
        this.wx.showModal({
            title: '支付提示',
            content: '很抱歉，功能正在紧急开发中，请您先用手机进行游戏充值',
            showCancel: false,
            confirmText: '确定',
            success(resp) {
                let billingResult = new Base();
                billingResult.parse(BaseCode.ThirdSDKReturnErrorCode.jsonWithMsg("platform not support."));
                callback(billingResult);
            },
        });
    }
    //微信支付
    billing(billingParams, callback) {
        const self = this;
        let billingResult = new Base();
        billingParams.platform = self.platform;
        billingParams.billingType = self.billingType;
        billingParams.billingEnv = ZeusServer.getBillingEnv(Module.WECHAT);
        ZeusServer.billing(this.wx, "wechat", billingParams, function (resp) {
            if (resp.statusCode == 200) {
                if (self.billingType == BillingType$1.ANDROID) {
                    let data = {
                        signData: resp.data.result.signData,
                        paySig: resp.data.result.paySig,
                        signature: resp.data.result.signature,
                    };
                    self.androidBillingProcess(data, callback);
                }
                else if (self.billingType == BillingType$1.IOS) {
                    self.iosBillingProcess(callback);
                }
                else {
                    self.unsupportedBillingProcess(callback);
                }
            }
            else {
                let res = {
                    code: BillingCode.ThirdSDKReturnErrorCode.Code,
                    msg: `billing failed with code: ${resp.statusCode}, data: ${JSON.stringify(resp.data)}`
                };
                billingResult.parse(res);
                callback(billingResult);
            }
        }, function (resp) {
            let res = {
                code: BillingCode.ThirdSDKReturnErrorCode.Code,
                msg: `billing failed with code: ${resp.errno}`
            };
            billingResult.parse(res);
            callback(billingResult);
        });
    }
    showRewardedAd(placement, callback) {
        this._adManager.showRewardedAd(placement, callback);
    }
    showInterstitialAd(placement, callback) {
        this._adManager.showInterstitialAd(placement, callback);
    }
    onNetworkStatusChange(callback) {
        this.wx.onNetworkStatusChange((res) => {
            let result = new Network();
            let resp = {
                code: BaseCode.Success.Code,
                network: res.networkType,
                msg: `network status changed, network: ${res.networkType}`
            };
            result.parse(resp);
            callback(result);
        });
    }
    openCustomerService() {
        this.wx.openCustomerServiceConversation({
            success: (res) => {
                Logger.debug("wechat open customer service successfully", res);
            },
            fail: (err) => {
                Logger.debug("wechat open customerServiceConversation failed", err);
            }
        });
    }
    openQuestionnaire(activityId, callback) {
        const appId = 'wxd947200f82267e58';
        const listener = (res) => {
            const referrerInfo = res.referrerInfo || {};
            const fromAppId = referrerInfo.appId;
            const extraData = referrerInfo.extraData || {};
            const scene = res.scene;
            let result = new Questionnaire();
            let jsonData;
            // 检查是否从问卷小程序返回
            if (fromAppId === appId && Math.abs(scene - 10037) < 0.2) {
                jsonData = BaseCode.Success.jsonWithMsg("Success");
            }
            else {
                jsonData = BaseCode.Cancel.jsonWithMsg(`back from appId: ${fromAppId}, scene: ${scene}`);
            }
            jsonData['result'] = extraData;
            jsonData['questionnaire_code'] = activityId;
            result.parse(jsonData);
            callback(result);
            this.wx.offShow(listener);
        };
        this.wx.onShow(listener);
        this.wx.navigateToMiniProgram({
            appId: appId,
            path: `pages/wjxqList/wjxqList?activityId=${activityId}&navigateBackMiniProgram=true`,
            success: (res) => {
                Logger.debug('navigate to questionnaire success', res);
            },
            fail: (err) => {
                Logger.error('navigate to questionnaire failed', err);
                let result = new Questionnaire();
                const jsonData = BaseCode.Cancel.jsonWithMsg(`Navigate failed: ${err.errMsg}`);
                result.parse(jsonData);
                callback(result);
                this.wx.offShow(listener);
            }
        });
    }
    checkUpdate() {
        const self = this;
        const updateManager = this.wx.getUpdateManager();
        updateManager.onCheckForUpdate(function (res) {
            // 请求完新版本信息的回调
            Logger.info("onCheckForUpdate:", res.hasUpdate);
        });
        updateManager.onUpdateReady(function () {
            self.wx.showModal({
                title: '更新提示',
                content: '新版本已经准备好，是否重启应用？',
                success: function (res) {
                    if (res.confirm) {
                        // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
                        updateManager.applyUpdate();
                    }
                }
            });
        });
        updateManager.onUpdateFailed(function () {
            // 新版本下载失败
            Logger.error('onUpdateFailed');
        });
    }
    setRankData(option) {
        const kvData = [];
        for (const key in option) {
            if (option.hasOwnProperty(key)) {
                kvData.push({
                    key: key,
                    value: String(option[key])
                });
            }
        }
        const opt = {
            KVDataList: kvData,
            complete: () => {
                Logger.info('setRankData complete');
            },
            success: (res) => {
                Logger.info('setRankData success', res);
            },
            fail: (err) => {
                Logger.error('setRankData fail', err);
            }
        };
        this.wx.setUserCloudStorage(opt);
    }
    showRankList(dict) {
        const option = new RankOption(dict);
        if (option.rankType == 'group') {
            option.shareTicket = this._shareTicket;
        }
        this.requirePrivacyAuthorize(() => {
            const msgData = Object.assign({}, option);
            this.getContext().postMessage(msgData);
        }, ({ errMsg }) => {
            Logger.error("user reject to auth privacy " + errMsg);
        });
    }
    requirePrivacyAuthorize(success, fail) {
        const opt = {
            complete: (result) => {
                Logger.info(`complete: ${JSON.stringify(result)}`);
            },
            success: (result) => {
                Logger.info(`success: ${JSON.stringify(result)}`);
                success(result.errMsg);
            },
            fail: (result) => {
                Logger.error(`fail: ${JSON.stringify(result)}`);
                fail(result.errMsg);
            }
        };
        this.wx.requirePrivacyAuthorize(opt);
    }
    hideOpenData() {
        this.requirePrivacyAuthorize(() => {
            const msgData = {
                type: 'engine',
                event: 'viewport',
                rankType: 'hide',
            };
            this.getContext().postMessage(msgData);
        }, ({ errMsg }) => {
            Logger.error("user reject to auth privacy " + errMsg);
        });
    }
    getContext() {
        if (!this._rankListOpenDataContext) {
            this._rankListOpenDataContext = this.wx.getOpenDataContext({
                sharedCanvasMode: 'screenCanvas'
            });
        }
        return this._rankListOpenDataContext;
    }
    subscribeMessage(tmplIds, callback) {
        this.wx.requestSubscribeMessage({
            tmplIds: tmplIds,
            success: (res) => {
                if (res.errMsg === 'requestSubscribeMessage:ok') {
                    let result = new Notification();
                    const jsonData = BaseCode.Success.jsonWithMsg("");
                    jsonData["topicResult"] = {};
                    // 复制除 errMsg 外的所有属性
                    for (const key in res) {
                        if (key !== 'errMsg') {
                            jsonData["topicResult"][key] = res[key];
                        }
                    }
                    result.parse(jsonData);
                    callback(result);
                }
            },
            fail: (err) => {
                let result = new Notification();
                const jsonData = BaseCode.ThirdSDKReturnErrorCode.jsonWithMsg("");
                jsonData["topicResult"] = {};
                // 对于失败情况，保持原有逻辑
                for (const key in err) {
                    if (key !== 'errMsg') {
                        jsonData["topicResult"][key] = err[key];
                    }
                }
                result.parse(jsonData);
                callback(result);
            }
        });
    }
    sendSubscribeMessage(openId, tmplId, callback) {
        ZeusServer.sendSubscribeMessage(this.wx, "wechat", openId, tmplId, function (resp) {
            let result = new Base();
            const jsonData = BaseCode.ThirdSDKReturnErrorCode.jsonWithMsg("");
            result.parse(jsonData);
            callback(result);
        }, function (resp) {
            let result = new Base();
            const jsonData = BaseCode.ZeusServiceFail.jsonWithMsg("");
            result.parse(jsonData);
            callback(result);
        });
    }
    share(options, callback) {
        this.wx.updateShareMenu({
            withShareTicket: true,
            success: (res) => {
                Logger.info('updateShareMenu res', res);
            }
        });
        this._shareStartTime = Date.now();
        this.wx.shareAppMessage({
            title: options.title,
            imageUrl: options.imageUrl,
            query: options.query,
        });
        const listener = function (shareStartTime) {
            const endTime = Date.now();
            const duration = (endTime - shareStartTime) / 1000;
            if (duration > 3) {
                let result = new Base();
                const jsonData = BaseCode.Success.jsonWithMsg("");
                result.parse(jsonData);
                callback(result);
            }
            else {
                let result = new Base();
                const jsonData = BaseCode.Cancel.jsonWithMsg("");
                result.parse(jsonData);
                callback(result);
            }
        };
        if (!this.hasAddedOnShowListener) {
            this.wx.onShow((res) => {
                listener(this._shareStartTime);
                this._shareTicket = res.shareTicket;
            });
            this.hasAddedOnShowListener = true;
        }
    }
    nativeToScene() {
        this.wx.showModal({
            title: '提示',
            content: '很抱歉，此功能暂不支持',
            showCancel: false,
            confirmText: '确定',
            success(resp) {
            },
        });
    }
    ;
}
WeChatAdapter.instance = null;

var BillingType;
(function (BillingType) {
    BillingType["IOS"] = "jsapi";
    BillingType["ANDROID"] = "virtual-payment";
    BillingType["UNKNOWN"] = "unknown";
})(BillingType || (BillingType = {}));
const globalContainer = GlobalContainer.getInstance();
class ByteDanceAdapter extends BaseAdapter {
    constructor() {
        super();
        this.loginType = "12";
        this._analytics = null;
        this.source_name = "douyin";
        this.tt = globalThis.tt;
        this._analytics = Analytics.getInstance();
        this._analytics.setSuperProperties({ "source_name": this.source_name });
    }
    get deviceInfo() {
        if (!this._deviceInfo) {
            if (this.tt.canIUse('getDeviceInfoSync')) {
                this._deviceInfo = this.tt.getDeviceInfoSync();
            }
            else if (this.tt.canIUse('getSystemInfoSync')) {
                this._deviceInfo = this.tt.getSystemInfoSync();
            }
        }
        return this._deviceInfo;
    }
    get platform() {
        var _a;
        return (_a = this.deviceInfo) === null || _a === void 0 ? void 0 : _a.platform;
    }
    static getInstance() {
        if (!this.instance) {
            this.instance = new ByteDanceAdapter();
        }
        return this.instance;
    }
    init(callback) {
        ZeusServer.gameInfo(this.tt, (res) => {
            var _a;
            Logger.debug("get game info successfully: ", res);
            const respResult = (_a = res.data) === null || _a === void 0 ? void 0 : _a.result;
            if (respResult) {
                if (respResult['douyinAdConfig']) {
                    globalContainer.setDouYinADConfig({
                        rewardAdUnitId: respResult['douyinAdConfig']['rewarded_ad_id'],
                        interstitialAdUnitId: respResult['douyinAdConfig']['interstitial_ad_id']
                    });
                }
            }
            respResult['code'] = BaseCode.Success.Code;
            this._isInit = true;
            this._adManager = getAdManager(false, this.tt, AdMedia.BYTE_DANCE);
            const tracker = ZeusTracker.getInstance();
            tracker.init();
            callback(respResult);
        }, (err) => {
            Logger.debug("get game info failed: ", err);
            let baserResult = new Base();
            const jsonData = BaseCode.ZeusServiceFail.jsonWithMsg(err.data.message);
            baserResult.parse(jsonData);
            callback(baserResult);
        });
    }
    login(callback) {
        const self = this;
        if (!self._isInit) {
            let result = new InitResult();
            result.parse(BaseCode.SDKNotInit.jsonWithMsg(''));
            callback(result);
            return;
        }
        Logger.debug("check douyin session status");
        this.tt.checkSession({
            success: function (res) {
                let account = LocalData.get(AccountConstants.LOCAL_ACCOUNT_KEY);
                Logger.debug("trying to get account from local storage: ", account, "result: ", res);
                if (account) {
                    Logger.debug("found account in local storage: ", account, " start verify login status with Zeus Server");
                    let loginResult = new Login();
                    ZeusServer.verifyLoginStatus(self.tt, account, function (res) {
                        var _a;
                        Logger.debug("verify login status successfully: ", res);
                        if (res && ((_a = res === null || res === void 0 ? void 0 : res.data) === null || _a === void 0 ? void 0 : _a.result)) {
                            res.data.result["login_type"] = LoginType.DouYin;
                            loginResult.parse(ZeusServer.normalizeZeusServerResponse(res));
                            self.loginSuccess(loginResult);
                            self._analytics.setSuperProperties({
                                "open_id": loginResult.Detail.openid,
                                "zeus_open_id": loginResult.Account,
                                "zeus_login_way": loginResult.LoginType,
                                "zeus_version": ZeusSDKVersion.SDKVersion,
                            });
                            self._analytics.login(account);
                            self._analytics.trackEvent(BIEvent.LOGIN_EVENT, {});
                            // 在静默登录场景下不会要求用户授权获取用户名称，这里用open id代替。
                            const props = { "user_name": loginResult.Detail.openid };
                            GravityEngineAgent.getInstance().setIdentifier(loginResult.Detail.openid, props);
                            callback(loginResult);
                        }
                        else {
                            Logger.debug("verify login status failed: ", res);
                            self._isLogin = false;
                            loginResult.parse(ZeusServer.normalizeZeusServerResponse(res));
                            callback(loginResult);
                        }
                    }, function (res) {
                        Logger.debug("verify login status failed: ", res);
                        self._isLogin = false;
                        loginResult.parse(ZeusServer.normalizeZeusServerResponse(res));
                        callback(loginResult);
                    });
                }
                else {
                    Logger.debug("no account in local storage: ", account, " start login with douyin");
                    self.doLogin(callback);
                }
            },
            fail: function (res) {
                self._isLogin = false;
                Logger.debug("douyin session check failed: ", res, " start login with douyin");
                self.doLogin(callback);
            }
        });
    }
    doLogin(callback) {
        const self = this;
        let loginResult = new Login();
        this.tt.login({
            success: function (res) {
                const loginParams = {
                    loginType: self.loginType,
                    code: res.code,
                    platform: self.platform,
                    mobileInfo: self.deviceInfo
                };
                Logger.debug("douyin login successfully: ", res, " start login with Zeus Server");
                ZeusServer.login(self.tt, loginParams, function (res) {
                    Logger.debug("Zeus Server login successfully: ", res);
                    res.data.result["login_type"] = LoginType.DouYin;
                    loginResult.parse(ZeusServer.normalizeZeusServerResponse(res));
                    self.loginSuccess(loginResult);
                    self._analytics.login(loginResult.Account);
                    self._analytics.setSuperProperties({
                        "open_id": loginResult.Detail.openid,
                        "zeus_open_id": loginResult.Account,
                        "zeus_login_way": loginResult.LoginType,
                        "zeus_version": ZeusSDKVersion.SDKVersion,
                    });
                    self._analytics.trackEvent(BIEvent.LOGIN_EVENT, {});
                    // 在静默登录场景下不会要求用户授权获取用户名称，这里用open id代替。
                    const props = { "user_name": loginResult.Detail.openid };
                    GravityEngineAgent.getInstance().setIdentifier(loginResult.Detail.openid, props);
                    if (loginResult.IsFirstRegister) {
                        GravityEngineAgent.getInstance().trackRegister();
                    }
                    callback(loginResult);
                }, function (res) {
                    Logger.debug("Zeus Server login failed: ", res);
                    self._isLogin = false;
                    loginResult.parse(ZeusServer.normalizeZeusServerResponse(res));
                    callback(loginResult);
                });
            },
            fail: function (err) {
                Logger.debug("douyin login failed: ", err);
                self._isLogin = false;
                const res = {
                    code: LoginCode.ThirdSDKReturnErrorCode.Code,
                    msg: `with code: ${err.errno} error: ${err.errMsg}`
                };
                loginResult.parse(res);
                callback(loginResult);
            },
            complete: function (res) {
                Logger.info('login complete', res);
            },
        });
    }
    loginSuccess(loginResult) {
        this._isLogin = true;
        const isSkipAd = loginResult.IsSkipAd;
        this._adManager = getAdManager(isSkipAd, this.tt, AdMedia.BYTE_DANCE);
        LocalData.save(AccountConstants.LOCAL_ACCOUNT_KEY, loginResult.Account);
        LocalData.save(AccountConstants.LOCAL_ID_KEY, loginResult.ID.toString());
    }
    doGetUserInfo(callback) {
        this.tt.getUserInfo({
            success: function (res) {
                Logger.info('getUserInfo success', res);
                let userInfoResult = new GetUserInfo();
                const result = {
                    code: BaseCode.Success.Code,
                    msg: res.errMsg,
                    errMsg: res.errMsg,
                    userInfo: res.userInfo
                };
                userInfoResult.parse(result);
                callback(userInfoResult);
            },
            fail: function (err) {
                Logger.error('getUserInfo fail', err);
                let userInfoResult = new GetUserInfo();
                const result = {
                    code: BaseCode.ThirdSDKReturnErrorCode.Code,
                    msg: err.errMsg,
                    errMsg: err.errMsg
                };
                userInfoResult.parse(result);
                callback(userInfoResult);
            },
        });
    }
    getUserInfo(callback) {
        if (this._isLogin) {
            this.tt.getSetting({
                success: (res) => {
                    if ((res === null || res === void 0 ? void 0 : res.authSetting['scope.userInfo']) === true) {
                        this.doGetUserInfo(callback);
                    }
                    else {
                        this.tt.authorize({
                            scope: 'scope.userInfo',
                            success: (data) => {
                                if ((data === null || data === void 0 ? void 0 : data.data['scope.userInfo']) === 'ok') {
                                    this.doGetUserInfo(callback);
                                }
                                else {
                                    let userInfoResult = new Base();
                                    userInfoResult.parse(BaseCode.UnknownHostError.jsonWithMsg("authorize failed"));
                                    callback(userInfoResult);
                                }
                            },
                            fail: (err) => {
                                this.tt.openSetting({
                                    success: (res) => {
                                        Logger.info('openSetting success', res);
                                    },
                                    fail: (err) => {
                                        let userInfoResult = new Base();
                                        userInfoResult.parse(BaseCode.ThirdSDKReturnErrorCode.jsonWithMsg(`err: ${JSON.stringify(err)} `));
                                        callback(userInfoResult);
                                    }
                                });
                            }
                        });
                    }
                },
                fail: (err) => {
                    let userInfoResult = new Base();
                    userInfoResult.parse(BaseCode.ThirdSDKReturnErrorCode.jsonWithMsg(`err: ${JSON.stringify(err)} `));
                    callback(userInfoResult);
                }
            });
        }
        else {
            let userInfoResult = new Base();
            userInfoResult.parse(BaseCode.UserNotLogin.jsonWithMsg(""));
            callback(userInfoResult);
        }
    }
    normalizeByteDanceBillingResponse(resp) {
        let res;
        // 成功的情况下不存在errCode字段
        if (resp.errCode === undefined) {
            res = {
                code: BillingCode.Success.Code,
                msg: `Success: ${resp.errMsg}`
            };
        }
        else {
            switch (resp.errCode) {
                case 0:
                    res = {
                        code: BillingCode.Success.Code,
                        msg: `with code: ${resp.errCode} error: ${resp.errMsg}`
                    };
                    break;
                case 1:
                case -2:
                    res = {
                        code: BillingCode.Cancel.Code,
                        msg: `with code: ${resp.errCode} error: ${resp.errMsg}`
                    };
                    break;
                default:
                    res = {
                        code: BillingCode.ThirdSDKReturnErrorCode.Code,
                        msg: `with code: ${resp.errCode} error: ${resp.errMsg}`
                    };
            }
        }
        return res;
    }
    androidBillingProcess(params, resp, callback) {
        var _a;
        // https://developer.open-douyin.com/docs/resource/zh-CN/mini-game/develop/api/payment/tt-request-game-payment
        const self = this;
        let billingResult = new Base();
        let ttParam = {
            mode: "game",
            env: 0,
            zoneId: "1",
            orderAmount: params.price,
            currencyType: (_a = params.currency) !== null && _a !== void 0 ? _a : "CNY", // ToDo iOS支持游戏币支付: DIAMOND
            goodName: params.productName,
            goodType: 2,
            platform: params.platform,
            customId: resp.data.result.order_id,
            extraInfo: params.extra,
            success: function (resp) {
                const res = self.normalizeByteDanceBillingResponse(resp);
                billingResult.parse(res);
                callback(billingResult);
            },
            fail: function (resp) {
                const res = self.normalizeByteDanceBillingResponse(resp);
                billingResult.parse(res);
                callback(billingResult);
            },
            complete: function (resp) {
                Logger.debug("requestGamePayment complete:", resp);
            }
        };
        self.tt.requestGamePayment(ttParam);
    }
    iosBillingProcess(params, resp, callback) {
        const self = this;
        let billingResult = new Base();
        let ttParam = {
            zoneId: "1",
            orderAmount: params.price, // 单位为分，最小值为10
            currencyType: "DIAMOND",
            goodName: params.productName,
            goodType: 2, // 道具直购场景
            customId: resp.data.result.order_id,
            extraInfo: params.extra,
            success: function (resp) {
                const res = self.normalizeByteDanceBillingResponse(resp);
                billingResult.parse(res);
                callback(billingResult);
            },
            fail: function (resp) {
                const res = self.normalizeByteDanceBillingResponse(resp);
                billingResult.parse(res);
                callback(billingResult);
            },
            complete: function (resp) {
                Logger.debug("openAwemeCustomerService complete:", resp);
            }
        };
        self.tt.openAwemeCustomerService(ttParam);
    }
    unsupportedBillingProcess(callback) {
        this.tt.showModal({
            title: '支付提示',
            content: '很抱歉，功能正在紧急开发中，请您先用手机进行游戏充值',
            showCancel: false,
            confirmText: '确定',
            success(resp) {
                let billingResult = new Base();
                billingResult.parse(BaseCode.ThirdSDKReturnErrorCode.jsonWithMsg("platform not support."));
                callback(billingResult);
            },
        });
    }
    billing(billingParams, callback) {
        var _a;
        const self = this;
        let billingResult = new Base();
        billingParams.platform = self.platform;
        billingParams.billingType = BillingType.ANDROID;
        billingParams.billingEnv = ZeusServer.getBillingEnv(Module.BYTE_DANCE);
        if (((_a = billingParams.productName) === null || _a === void 0 ? void 0 : _a.length) > 10) {
            let res = {
                code: BillingCode.ParamError.Code,
                msg: `The length of the "productName" cannot exceed 10 characters`
            };
            billingResult.parse(res);
            callback(billingResult);
        }
        else {
            ZeusServer.billing(this.tt, "douyin", billingParams, function (resp) {
                if (resp.statusCode == 200) {
                    if (self.platform == 'android') {
                        self.androidBillingProcess(billingParams, resp, callback);
                    }
                    else if (self.platform == 'ios') {
                        self.iosBillingProcess(billingParams, resp, callback);
                    }
                    else {
                        self.unsupportedBillingProcess(callback);
                    }
                }
                else {
                    let res = {
                        code: BillingCode.ThirdSDKReturnErrorCode.Code,
                        msg: `billing failed with code: ${resp.statusCode}, data: ${resp.data}`
                    };
                    billingResult.parse(res);
                    callback(billingResult);
                }
            }, function (resp) {
                let res = {
                    code: BillingCode.ThirdSDKReturnErrorCode.Code,
                    msg: `billing failed with code: ${resp.errno}`
                };
                billingResult.parse(res);
                callback(billingResult);
            });
        }
    }
    showRewardedAd(placement, callback) {
        this._adManager.showRewardedAd(placement, callback);
    }
    showInterstitialAd(placement, callback) {
        this._adManager.showInterstitialAd(placement, callback);
    }
    onNetworkStatusChange(callback) {
        this.tt.onNetworkStatusChange((res) => {
            let result = new Network();
            let resp = {
                code: BaseCode.Success.Code,
                network: res.networkType,
                msg: `network status changed, network: ${res.networkType}`
            };
            result.parse(resp);
            callback(result);
        });
    }
    openCustomerService(callback) {
        let options = {
            type: 1, // 小6客服
            success() {
                let result = new Base();
                const res = {
                    code: BaseCode.Success.Code,
                    msg: 'openCustomerService success'
                };
                result.parse(res);
                callback(result);
            },
            fail(err) {
                let result = new Base();
                const res = {
                    code: BaseCode.ThirdSDKReturnErrorCode.Code,
                    msg: 'openCustomerService fail'
                };
                result.parse(res);
                callback(result);
            },
        };
        this.tt.openCustomerServiceConversation(options);
    }
    openQuestionnaire(activityId, callback) {
        this.tt.showToast({
            title: "暂不可跳转",
            icon: "none",
        });
    }
    checkUpdate() {
        const self = this;
        const updateManager = this.tt.getUpdateManager();
        updateManager.onCheckForUpdate((res) => {
            Logger.info("onCheckForUpdate:", res.hasUpdate);
        });
        updateManager.onUpdateReady(function () {
            self.tt.showModal({
                title: '更新提示',
                content: '新版本已经准备好，是否重启应用？',
                success: function (res) {
                    if (res.confirm) {
                        // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
                        updateManager.applyUpdate();
                    }
                }
            });
        });
        updateManager.onUpdateFailed((err) => {
            // 新的版本下载失败
            Logger.error("版本下载失败原因", err);
            self.tt.showToast({
                title: "新版本下载失败，请稍后再试",
                icon: "none",
            });
        });
    }
    setRankData(rankData) {
        const data = {
            dataType: rankData['dataType'],
            value: rankData['value'],
            priority: rankData['priority'] || 0,
            zoneId: rankData['zoneId'] || 'default',
            success(res) {
                Logger.info('setImRankData success', res === null || res === void 0 ? void 0 : res.errMsg);
            },
            fail(err) {
                Logger.error('setImRankData fail:', err === null || err === void 0 ? void 0 : err.errMsg);
            }
        };
        this.tt.setImRankData(data);
    }
    showRankList(option) {
        const params = {
            dataType: option['dataType'],
            relationType: option['relationType'],
            rankType: option['rankType'],
            suffix: undefined,
            rankTitle: undefined,
            zoneId: undefined,
            success(res) {
                Logger.info('showRankList success', res);
            },
            fail(err) {
                Logger.error('showRankList fail', err);
            }
        };
        if (option['suffix']) {
            params['suffix'] = option['suffix'];
        }
        if (option['rankTitle']) {
            params['rankTitle'] = option['rankTitle'];
        }
        if (option['zoneId']) {
            params['zoneId'] = option['zoneId'];
        }
        this.tt.getImRankList(params);
    }
    share(options, callback) {
        let result = new Base();
        this.tt.shareAppMessage(Object.assign(Object.assign({}, options), { success() {
                const jsonData = BaseCode.Success.jsonWithMsg("");
                result.parse(jsonData);
                callback(result);
            },
            fail(err) {
                var _a;
                // 检查是否是用户取消
                if (((_a = err === null || err === void 0 ? void 0 : err.errMsg) === null || _a === void 0 ? void 0 : _a.includes('cancel')) || (err === null || err === void 0 ? void 0 : err.errNo) === 10502) {
                    const res = {
                        code: BaseCode.Cancel.Code, // 使用取消状态码
                        msg: 'User cancelled sharing'
                    };
                    result.parse(res);
                }
                else {
                    const res = {
                        code: BaseCode.ThirdSDKReturnErrorCode.Code,
                        msg: (err === null || err === void 0 ? void 0 : err.errMsg) || 'shareAppMessage fail'
                    };
                    result.parse(res);
                }
                callback(result);
            } }));
    }
    nativeToScene() {
        this.tt.navigateToScene({
            scene: 'sidebar',
            success(res) {
                Logger.info('navigateToScene success', res);
            },
            fail(err) {
                Logger.error('navigateToScene fail', err);
            }
        });
    }
    subscribeMessage(tmplIds, callback) {
        this.tt.requestSubscribeMessage({
            tmplIds: tmplIds,
            success: (res) => {
                Logger.info('requestSubscribeMessage success', res);
                if (res.errMsg === 'requestSubscribeMessage:ok') {
                    let result = new Notification();
                    const jsonData = BaseCode.Success.jsonWithMsg("");
                    jsonData["topicResult"] = {};
                    // 复制除 errMsg 外的所有属性
                    for (const key in res) {
                        if (key !== 'errMsg') {
                            jsonData["topicResult"][key] = res[key];
                        }
                    }
                    result.parse(jsonData);
                    callback(result);
                }
            },
            fail: (err) => {
                Logger.error('requestSubscribeMessage fail', err);
                let result = new Notification();
                const jsonData = BaseCode.ThirdSDKReturnErrorCode.jsonWithMsg(err === null || err === void 0 ? void 0 : err.message);
                jsonData["topicResult"] = {};
                // 对于失败情况，保持原有逻辑
                for (const key in err) {
                    if (key !== 'errMsg') {
                        jsonData["topicResult"][key] = err[key];
                    }
                }
                result.parse(jsonData);
                callback(result);
            }
        });
    }
    hideOpenData() {
        this.tt.showModal({
            title: '提示',
            content: '很抱歉，此功能暂不支持',
            showCancel: false,
            confirmText: '确定',
            success() {
            },
        });
    }
    ;
}
ByteDanceAdapter.instance = null;

class PlatformFactory {
    getInstance() {
        try {
            if (Device.isWeChat()) {
                return WeChatAdapter.getInstance();
            }
            else if (Device.isByteDance()) {
                return ByteDanceAdapter.getInstance();
            }
            else {
                throw new Error("platform not support");
            }
        }
        catch (err) {
            throw err;
        }
    }
}

class GraniteWrapper {
    static init(appModel, callback) {
        if (!appModel.AppId || !appModel.AppKey || !appModel.AppRequestURL) {
            const result = new InitResult();
            result.parse(BaseCode.SDKNotInit.jsonWithMsg(`SDK initialization failed: Missing parameters AppId, AppKey or AppRequestURL.`));
            callback(result);
            return;
        }
        const result = this.initComponents();
        if (result != null) {
            callback(result);
        }
        else {
            const globalContainer = GlobalContainer.getInstance();
            globalContainer.setAppModel(appModel);
            this.baseInit(callback);
        }
    }
    static baseInit(callback) {
        this.AppPlatform.init((res) => {
            let result = new InitResult();
            result.parse(res);
            callback(result);
        });
    }
    static getVersion() {
        return ZeusSDKVersion.SDKVersion;
    }
    static initComponents() {
        let result;
        try {
            this.AppPlatform = new PlatformFactory().getInstance();
            this.Analytics = Analytics.getInstance();
            this.Attribution = GravityEngineAgent.getInstance();
        }
        catch (err) {
            result = new InitResult();
            result.parse(BaseCode.SDKNotInit.jsonWithMsg(`SDK initialization failed: ${err.message}`));
        }
        return result;
    }
}

export { GraniteWrapper };
