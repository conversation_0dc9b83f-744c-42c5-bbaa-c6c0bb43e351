"use strict";function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function _createClass(e,t,i){return t&&_defineProperties(e.prototype,t),i&&_defineProperties(e,i),e}var Config={LIB_VERSION:"3.3.0-beta.2",LIB_NAME:"MG"},_={},ArrayProto=Array.prototype,ObjProto=Object.prototype,slice=ArrayProto.slice,nativeToString=ObjProto.toString,nativeHasOwnProperty=Object.prototype.hasOwnProperty,nativeForEach=ArrayProto.forEach,nativeIsArray=Array.isArray,breaker={},utmTypes=["utm_source","utm_medium","utm_campaign","utm_content","utm_term"];_.each=function(e,t,i){if(null==e)return!1;if(nativeForEach&&e.forEach===nativeForEach)e.forEach(t,i);else if(e.length===+e.length){for(var n=0,a=e.length;n<a;n++)if(n in e&&t.call(i,e[n],n,e)===breaker)return!1}else for(var r in e)if(nativeHasOwnProperty.call(e,r)&&t.call(i,e[r],r,e)===breaker)return!1},_.extend=function(i){return _.each(slice.call(arguments,1),function(e){for(var t in e)void 0!==e[t]&&(i[t]=e[t])}),i},_.extend2Layers=function(i){return _.each(slice.call(arguments,1),function(e){for(var t in e)void 0!==e[t]&&(_.isObject(e[t])&&_.isObject(i[t])?_.extend(i[t],e[t]):i[t]=e[t])}),i},_.isArray=nativeIsArray||function(e){return"[object Array]"===nativeToString.call(e)},_.isFunction=function(e){try{return"function"==typeof e}catch(e){return!1}},_.isPromise=function(e){return"[object Promise]"===nativeToString.call(e)&&null!=e},_.isObject=function(e){return"[object Object]"===nativeToString.call(e)&&null!=e},_.isEmptyObject=function(e){if(_.isObject(e)){for(var t in e)if(nativeHasOwnProperty.call(e,t))return!1;return!0}return!1},_.isUndefined=function(e){return void 0===e},_.isString=function(e){return"[object String]"===nativeToString.call(e)},_.isDate=function(e){return"[object Date]"===nativeToString.call(e)},_.isBoolean=function(e){return"[object Boolean]"===nativeToString.call(e)},_.isNumber=function(e){return"[object Number]"===nativeToString.call(e)&&/[\d\.]+/.test(String(e))},_.isJSONString=function(e){try{JSON.parse(e)}catch(e){return!1}return!0},_.decodeURIComponent=function(t){var i="";try{i=decodeURIComponent(t)}catch(e){i=t}return i},_.encodeURIComponent=function(t){var i="";try{i=encodeURIComponent(t)}catch(e){i=t}return i},_.utf8Encode=function(e){for(var t,i="",n=t=0,a=(e=(e+"").replace(/\r\n/g,"\n").replace(/\r/g,"\n")).length,r=0;r<a;r++){var s=e.charCodeAt(r),o=null;s<128?t++:o=127<s&&s<2048?String.fromCharCode(s>>6|192,63&s|128):String.fromCharCode(s>>12|224,s>>6&63|128,63&s|128),null!==o&&(n<t&&(i+=e.substring(n,t)),i+=o,n=t=r+1)}return n<t&&(i+=e.substring(n,e.length)),i},_.base64Encode=function(e){var t,i,n,a,r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",s=0,o=0,c="",l=[];if(!e)return e;for(e=_.utf8Encode(e);t=(a=e.charCodeAt(s++)<<16|e.charCodeAt(s++)<<8|e.charCodeAt(s++))>>18&63,i=a>>12&63,n=a>>6&63,a=63&a,l[o++]=r.charAt(t)+r.charAt(i)+r.charAt(n)+r.charAt(a),s<e.length;);switch(c=l.join(""),e.length%3){case 1:c=c.slice(0,-2)+"==";break;case 2:c=c.slice(0,-1)+"="}return c},_.encodeDates=function(n){return _.each(n,function(e,t){if(_.isDate(e))n[t]=_.formatDate(e);else if(_.isObject(e))n[t]=_.encodeDates(e);else if(_.isArray(e))for(var i=0;i<e.length;i++)_.isDate(e[i])&&(n[t][i]=_.formatDate(e[i]))}),n},_.formatDate=function(e){function t(e){return e<10?"0"+e:e}return e.getFullYear()+"-"+t(e.getMonth()+1)+"-"+t(e.getDate())+" "+t(e.getHours())+":"+t(e.getMinutes())+":"+t(e.getSeconds())+"."+((i=e.getMilliseconds())<100&&9<i?"0"+i:i<10?"00"+i:i);var i},_.formatTimeZone=function(e,t){if("number"!=typeof t)return e;var i=e.getTime(),e=6e4*e.getTimezoneOffset();return new Date(i+e+36e5*t)},_.getTimeZone=function(e,t){return"number"==typeof t?t:0-e.getTimezoneOffset()/60},_.searchObjDate=function(i,n){try{(_.isObject(i)||_.isArray(i))&&_.each(i,function(e,t){_.isObject(e)||_.isArray(e)?_.searchObjDate(i[t],n):_.isDate(e)&&(i[t]=_.formatDate(_.formatTimeZone(e,n)))})}catch(e){logger$1.warn(e)}},_.UUID=function(){var e=(new Date).getTime();return String(Math.random()).replace(".","").slice(1,11)+"-"+e},_.UUIDv4=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})},_.setMpPlatform=function(e){_.mpPlatform=e},_.getMpPlatform=function(){return _.mpPlatform},_.createExtraHeaders=function(){return{"TA-Integration-Type":Config.LIB_NAME,"TA-Integration-Version":Config.LIB_VERSION,"TA-Integration-Count":"1","TA-Integration-Extra":_.getMpPlatform()}},_.checkAppId=function(e){if(e)return e=e.replace(/\s+/g,"")},_.checkUrl=function(e){if(e)return e=e.replace(/\s+/g,""),e=_.url("basic",e)},_.url=function(){function a(){return new RegExp(/(.*?)\.?([^.]*?)\.(com|net|org|biz|ws|in|me|co\.uk|co|org\.uk|ltd\.uk|plc\.uk|me\.uk|edu|mil|br\.com|cn\.com|eu\.com|hu\.com|no\.com|qc\.com|sa\.com|se\.com|se\.net|us\.com|uy\.com|ac|co\.ac|gv\.ac|or\.ac|ac\.ac|af|am|as|at|ac\.at|co\.at|gv\.at|or\.at|asn\.au|com\.au|edu\.au|org\.au|net\.au|id\.au|be|ac\.be|adm\.br|adv\.br|am\.br|arq\.br|art\.br|bio\.br|cng\.br|cnt\.br|com\.br|ecn\.br|eng\.br|esp\.br|etc\.br|eti\.br|fm\.br|fot\.br|fst\.br|g12\.br|gov\.br|ind\.br|inf\.br|jor\.br|lel\.br|med\.br|mil\.br|net\.br|nom\.br|ntr\.br|odo\.br|org\.br|ppg\.br|pro\.br|psc\.br|psi\.br|rec\.br|slg\.br|tmp\.br|tur\.br|tv\.br|vet\.br|zlg\.br|br|ab\.ca|bc\.ca|mb\.ca|nb\.ca|nf\.ca|ns\.ca|nt\.ca|on\.ca|pe\.ca|qc\.ca|sk\.ca|yk\.ca|ca|cc|ac\.cn|net\.cn|com\.cn|edu\.cn|gov\.cn|org\.cn|bj\.cn|sh\.cn|tj\.cn|cq\.cn|he\.cn|nm\.cn|ln\.cn|jl\.cn|hl\.cn|js\.cn|zj\.cn|ah\.cn|gd\.cn|gx\.cn|hi\.cn|sc\.cn|gz\.cn|yn\.cn|xz\.cn|sn\.cn|gs\.cn|qh\.cn|nx\.cn|xj\.cn|tw\.cn|hk\.cn|mo\.cn|cn|cx|cz|de|dk|fo|com\.ec|tm\.fr|com\.fr|asso\.fr|presse\.fr|fr|gf|gs|co\.il|net\.il|ac\.il|k12\.il|gov\.il|muni\.il|ac\.in|co\.in|org\.in|ernet\.in|gov\.in|net\.in|res\.in|is|it|ac\.jp|co\.jp|go\.jp|or\.jp|ne\.jp|ac\.kr|co\.kr|go\.kr|ne\.kr|nm\.kr|or\.kr|li|lt|lu|asso\.mc|tm\.mc|com\.mm|org\.mm|net\.mm|edu\.mm|gov\.mm|ms|nl|no|nu|pl|ro|org\.ro|store\.ro|tm\.ro|firm\.ro|www\.ro|arts\.ro|rec\.ro|info\.ro|nom\.ro|nt\.ro|se|si|com\.sg|org\.sg|net\.sg|gov\.sg|sk|st|tf|ac\.th|co\.th|go\.th|mi\.th|net\.th|or\.th|tm|to|com\.tr|edu\.tr|gov\.tr|k12\.tr|net\.tr|org\.tr|com\.tw|org\.tw|net\.tw|ac\.uk|uk\.com|uk\.net|gb\.com|gb\.net|vg|sh|kz|ch|info|ua|gov|name|pro|ie|hk|com\.hk|org\.hk|net\.hk|edu\.hk|us|tk|cd|by|ad|lv|eu\.lv|bz|es|jp|cl|ag|mobi|eu|co\.nz|org\.nz|net\.nz|maori\.nz|iwi\.nz|io|la|md|sc|sg|vc|tw|travel|my|se|tv|pt|com\.pt|edu\.pt|asia|fi|com\.ve|net\.ve|fi|org\.ve|web\.ve|info\.ve|co\.ve|tel|im|gr|ru|net\.ru|org\.ru|hr|com\.hr|ly|xyz)$/)}function r(e,t){var i=e.charAt(0),t=t.split(i);return i===e?t:t[(e=parseInt(e.substring(1),10))<0?t.length+e:e-1]}function s(e,t){for(var i,n,a=e.charAt(0),r=t.split("&"),s=[],o={},c=e.substring(1),l=0,u=r.length;l<u;l++)if(""!==(s=(s=r[l].match(/(.*?)=(.*)/))||[r[l],r[l],""])[1].replace(/\s/g,"")){if(s[2]=(n=s[2]||"",_.decodeURIComponent(n.replace(/\+/g," "))),c===s[1])return s[2];(i=s[1].match(/(.*)\[([0-9]+)\]/))?(o[i[1]]=o[i[1]]||[],o[i[1]][i[2]]=s[2]):o[s[1]]=s[2]}return a===e?o:o[c]}return function(e,t){var i={};if("tld?"===e)return a();if(t=t||window.location.toString(),!e)return t;if(e=e.toString(),t.match(/^mailto:([^/].+)/))n=t.match(/^mailto:([^/].+)/),i.protocol="mailto",i.email=n[1];else{if(t.match(/(.*?)\/#!(.*)/)&&(t=(n=t.match(/(.*?)\/#!(.*)/))[1]+n[2]),t.match(/(.*?)#(.*)/)&&(n=t.match(/(.*?)#(.*)/),i.hash=n[2],t=n[1]),i.hash&&e.match(/^#/))return s(e,i.hash);if(t.match(/(.*?)\?(.*)/)&&(n=t.match(/(.*?)\?(.*)/),i.query=n[2],t=n[1]),i.query&&e.match(/^\?/))return s(e,i.query);if(t.match(/(.*?):?\/\/(.*)/)&&(n=t.match(/(.*?):?\/\/(.*)/),i.protocol=n[1].toLowerCase(),t=n[2]),t.match(/(.*?)(\/.*)/)&&(n=t.match(/(.*?)(\/.*)/),i.path=n[2],t=n[1]),i.path=(i.path||"").replace(/^([^/])/,"/$1").replace(/\/$/,""),e.match(/^[-0-9]+$/)&&(e=e.replace(/^([^/])/,"/$1")),e.match(/^\//))return r(e,i.path.substring(1));if((n=(n=r("/-1",i.path.substring(1)))&&n.match(/(.*?)\.(.*)/))&&(i.file=n[0],i.filename=n[1],i.fileext=n[2]),t.match(/(.*):([0-9]+)$/)&&(n=t.match(/(.*):([0-9]+)$/),i.port=n[2],t=n[1]),t.match(/(.*?)@(.*)/)&&(n=t.match(/(.*?)@(.*)/),i.auth=n[1],t=n[2]),i.auth&&(n=i.auth.match(/(.*):(.*)/),i.user=n?n[1]:i.auth,i.pass=n?n[2]:void 0),i.hostname=t.toLowerCase(),"."===e.charAt(0))return r(e,i.hostname);a()&&(n=i.hostname.match(a()))&&(i.tld=n[3],i.domain=n[2]?n[2]+"."+n[3]:void 0,i.sub=n[1]||void 0);var n=i.port?":"+i.port:"";i.protocol=i.protocol||window.location.protocol.replace(":",""),i.port=i.port||("https"===i.protocol?"443":"80"),i.protocol=i.protocol||("443"===i.port?"https":"http"),i.basic=i.protocol+"://"+i.hostname+n}return e in i?i[e]:"{}"===e?i:""}}(),_.createString=function(e){for(var t=e,i=Math.random().toString(36).substr(2);i.length<t;)i+=Math.random().toString(36).substr(2);return i=i.substr(0,e)},_.createAesKey=function(){return _.createString(16)},_.generateEncryptyData=function(e,t){if(void 0===t)return e;var i=t.publicKey,n=t.version;if(void 0===i||void 0===n)return e;if("undefined"==typeof CryptoJS||"undefined"==typeof JSEncrypt)return e;var a=_.createAesKey();try{var r=CryptoJS.enc.Utf8.parse(a),s=CryptoJS.enc.Utf8.parse(JSON.stringify(e)),o=_.isUndefined(CryptoJS.pad.Pkcs7)?CryptoJS.pad.PKCS7:CryptoJS.pad.Pkcs7,r=CryptoJS.AES.encrypt(s,r,{mode:CryptoJS.mode.ECB,padding:o}).toString(),o=new JSEncrypt;o.setPublicKey(i);o=o.encrypt(a);return!1===o?(logger$1.warn("Encryption failed, return the original data"),e):{pkv:n,ekey:o,payload:r}}catch(e){logger$1.warn("Encryption failed, return the original data: "+e)}return e},_.getUtm=function(){var i={};return _.each(utmTypes,function(e){try{var t=_.getQueryParam(location.href,e);t.length&&(i[e]=t)}catch(e){logger$1.warn("get utm fail: "+e)}}),JSON.stringify(i)},_.getQueryParam=function(e,t){t=t.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]"),e=_.decodeURIComponent(e);e=new RegExp("[\\?&]"+t+"=([^&#]*)").exec(e);return null===e||e&&"string"!=typeof e[1]&&e[1].length?"":_.decodeURIComponent(e[1])},_.getUtmFromQuery=function(t){var i={};return _.each(utmTypes,function(e){t[e]&&(i[e]=t[e])}),JSON.stringify(i)},_.indexOf=function(e,t){var i=e.indexOf;if(i)return i.call(e,t);for(var n=0;n<e.length;n++)if(t===e[n])return n;return-1},_.checkCalibration=function(e,t,i){return e},_.isClickType=function(e){return{tap:1,longpress:1,longtap:1}[e]},_.getCurrentTimeStamp=function(){return Date.now()},_.getCurrentDate=function(){return new Date(Date.now())};var logger$1="object"===_typeof(logger$1)?logger$1:{};logger$1.info=function(){if("object"===("undefined"==typeof console?"undefined":_typeof(console))&&console.log&&logger$1.enabled)try{return arguments[0]="[ThinkingData] Info: "+arguments[0],logger$1.listener&&logger$1.listener(arguments[0]),console.log.apply(console,arguments)}catch(e){logger$1.listener&&logger$1.listener(arguments[0]),console.log("[ThinkingData] Info: "+arguments[0])}},logger$1.warn=function(){if("object"===("undefined"==typeof console?"undefined":_typeof(console))&&console.log&&logger$1.enabled)try{return arguments[0]="[ThinkingData] Warning: "+arguments[0],console.warn.apply(console,arguments)}catch(e){console.warn("[ThinkingData] Warning: "+arguments[0])}};var PlatformProxy=function(){function e(){_classCallCheck(this,e),this.config={persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_mg"}}return _createClass(e,[{key:"getConfig",value:function(){return this.config}},{key:"initSdkConfig",value:function(){}},{key:"getStorage",value:function(e,t,i){e=localStorage.getItem(e);if(!t)return _.isJSONString(e)?JSON.parse(e):{};_.isJSONString(e)?i(JSON.parse(e)):i({})}},{key:"setStorage",value:function(e,t){localStorage.setItem(e,t)}},{key:"removeStorage",value:function(e){localStorage.removeItem(e)}},{key:"_setSystemProxy",value:function(e){this._sysCallback=e}},{key:"getSystemInfo",value:function(e){var t={mp_platform:"web",system:this._getOs(),screenWidth:window.screen.width,screenHeight:window.screen.height,systemLanguage:navigator.language};this._sysCallback&&(t=_.extend(t,this._sysCallback(e))),e.success(t),e.complete()}},{key:"_getOs",value:function(){var e=navigator.userAgent;return/Windows/i.test(e)?/Phone/.test(e)||/WPDesktop/.test(e)?"Windows Phone":"Windows":/(iPhone|iPad|iPod)/.test(e)?"iOS":/Android/.test(e)?"Android":/(BlackBerry|PlayBook|BB10)/i.test(e)?"BlackBerry":/Mac/i.test(e)?"MacOS":/Linux/.test(e)?"Linux":/CrOS/.test(e)?"ChromeOS":""}},{key:"getNetworkType",value:function(e){e.complete()}},{key:"onNetworkStatusChange",value:function(){}},{key:"request",value:function(e){var t={},i=new XMLHttpRequest;if(i.open(e.method,e.url),e.header)for(var n in e.header)i.setRequestHeader(n,e.header[n]);return i.onreadystatechange=function(){4===i.readyState&&200===i.status?(t.statusCode=200,_.isJSONString(i.responseText)&&(t.data=JSON.parse(i.responseText)),e.success(t)):200!==i.status&&(t.errMsg="network error",e.fail(t))},i.ontimeout=function(){t.errMsg="timeout",e.fail(t)},i.send(e.data),i}},{key:"initAutoTrackInstance",value:function(e,t){this.instance=e,this.autoTrack=t.autoTrack;var i=this;i.onPageShow(),i.autoTrack.appHide&&i.instance.timeEvent("ta_page_hide"),"onvisibilitychange"in document&&(document.onvisibilitychange=function(){document.hidden?i.onPageHide(!0):(i.onPageShow(),i.autoTrack.appHide&&i.instance.timeEvent("ta_page_hide"))})}},{key:"setGlobal",value:function(e,t){window[t]=e}},{key:"getAppOptions",value:function(){}},{key:"showToast",value:function(){}},{key:"onPageShow",value:function(){var e;this.autoTrack.appShow&&(e={},_.extend(e,this.autoTrack.properties),_.isFunction(this.autoTrack.callback)&&_.extend(e,this.autoTrack.callback("appShow")),this.instance._internalTrack("ta_page_show",e))}},{key:"onPageHide",value:function(e){var t;this.autoTrack.appHide&&(t={},_.extend(t,this.autoTrack.properties),_.isFunction(this.autoTrack.callback)&&_.extend(t,this.autoTrack.callback("appHide")),this.instance._internalTrack("ta_page_hide",t,new Date,null,e))}},{key:"setGlobalData",value:function(){}}],[{key:"createInstance",value:function(){return new e}}]),e}(),mpHooks={data:1,onLoad:1,onShow:1,onReady:1,onPullDownRefresh:1,onShareAppMessage:1,onShareTimeline:1,onReachBottom:1,onPageScroll:1,onResize:1,onTabItemTap:1,onHide:1,onUnload:1,onAddToFavorites:1},AutoTrackBridge=function(){function i(e,t){_classCallCheck(this,i),this.taInstance=e,this.config=t.autoTrack||{},this.disablePresetList=t.disablePresetProperties||[],this.referrer="Directly open",this.config.isPlugin?(e.App=function(){App.apply(this,arguments)},inension(e.Page)):(e=App,App=this._initAppExtention(e),e=Page,Page=this._initPageExtension(e))}return _createClass(i,[{key:"_initPageExtension",value:function(r){var s=this;return function(e){var t=e.onShow,i=e.onShareAppMessage,n=e.onUnload,a=e.onAddToFavorites;return e.onShow=function(e){s.onPageShow(),"function"==typeof t&&t.call(this,e)},"function"==typeof i&&(e.onShareAppMessage=function(e){e=i.call(this,e);return s.onPageShare(e)}),e.onUnload=function(){s.onPageUnload(),"function"==typeof n&&n.call(this)},e.onAddToFavorites=function(){s.onPageAddToFavorites(),"function"==typeof a&&a.call(this)},s._handleClickProxy(e),r(e)}}},{key:"_handleClickProxy",value:function(e){if(this.config.mpClick){var t,i=[];for(t in e)"function"!=typeof e[t]||mpHooks[t]||i.push(t);for(var n=0;n<i.length;n++)this.clickMethodProxy(e,i[n])}}},{key:"clickMethodProxy",value:function(e,t){var i=this,n=e[t];e[t]=function(){var e=n.call(this,arguments),t=arguments[0];return _.isObject(t)&&i._trackClickEvent(t),e}}},{key:"_trackClickEvent",value:function(e){var t=e.currentTarget||{},i=e.target||{};i.id&&t.id&&i.id!==t.id||(i={},(e=e.type)&&_.isClickType(e)&&(e=t.dataset||{},this.disablePresetList.includes("#element_id")||(i["#element_id"]=t.id),this.disablePresetList.includes("#element_type")||(i["#element_type"]=e.type),this.disablePresetList.includes("#element_content")||(i["#element_content"]=e.content),this.disablePresetList.includes("#element_name")||(i["#element_name"]=e.name),this.disablePresetList.includes("#url_path")||(i.$url_path=this._getCurrentPath()),_.extend(i,this.config.properties),_.isFunction(this.config.callback)&&_.extend(i,this.config.callback("mpClick")),this.taInstance._internalTrack("ta_mp_click",i)))}},{key:"_initAppExtention",value:function(a){var r=this;return function(e){var t=e.onLaunch,i=e.onShow,n=e.onHide;return e.onLaunch=function(e){r.onAppLaunch(e,this),"function"==typeof t&&t.call(this,e)},e.onShow=function(e){r.onAppShow(e),"function"==typeof i&&i.call(this,e)},e.onHide=function(){r.onAppHide(),"function"==typeof n&&n.call(this)},a(e)}}},{key:"onAppLaunch",value:function(e,t){this._setAutoTrackProperties(e),_.isUndefined(t)||(t[this.taInstance.name]=this.taInstance),this.config.appLaunch&&(t={},this.disablePresetList.includes("#url_path")||e&&e.path&&(t["#url_path"]=this._getPath(e.path)),e&&(this.disablePresetList.includes("#utm")||e.query&&(t["#utm"]=_.getUtmFromQuery(e.query)),this.disablePresetList.includes("#start_reason")||(t["#start_reason"]=JSON.stringify(e))),_.extend(t,this.config.properties),_.isFunction(this.config.callback)&&_.extend(t,this.config.callback("appLaunch")),this.taInstance._internalTrack("ta_mp_launch",t))}},{key:"onAppShow",value:function(e){var t;this.config.appHide&&this.taInstance.timeEvent("ta_mp_hide"),this._setAutoTrackProperties(e),this.config.appShow&&(t={},this.disablePresetList.includes("#url_path")||e&&e.path&&(t["#url_path"]=this._getPath(e.path)),e&&(this.disablePresetList.includes("#utm")||e.query&&(t["#utm"]=_.getUtmFromQuery(e.query)),this.disablePresetList.includes("#start_reason")||(t["#start_reason"]=JSON.stringify(e))),_.extend(t,this.config.properties),_.isFunction(this.config.callback)&&_.extend(t,this.config.callback("appShow")),this.taInstance._internalTrack("ta_mp_show",t))}},{key:"onAppHide",value:function(){var e;this.config.appHide&&(e={},this.disablePresetList.includes("#url_path")||(e["#url_path"]=this._getCurrentPath()),_.extend(e,this.config.properties),_.isFunction(this.config.callback)&&_.extend(e,this.config.callback("appHide")),this.taInstance._internalTrack("ta_mp_hide",e),this.taInstance.flush())}},{key:"_getCurrentPath",value:function(){var e="Not to get";try{var t=getCurrentPages(),e=t[t.length-1].route}catch(e){logger$1.info(e)}return e}},{key:"_setAutoTrackProperties",value:function(e){var t={};this.disablePresetList.includes("#scene")||(t["#scene"]=e.scene),this.taInstance._setAutoTrackProperties(t)}},{key:"_getPath",value:function(e){return"string"==typeof e?e.replace(/^\//,""):"Abnormal values"}},{key:"_generateShareInfo",value:function(){return JSON.stringify({distinctId:this.taInstance.getDistinctId()})}},{key:"onPageShare",value:function(e){var t=_.isObject(e)?e:{};return this.config.pageShare&&(e={},this.disablePresetList.includes("#url_path")||(e["#url_path"]=this._getCurrentPath()),_.extend(e,this.config.properties),_.isFunction(this.config.callback)&&_.extend(e,this.config.callback("pageShare")),this.taInstance._internalTrack("ta_mp_share",e),!_.isUndefined(t.path)&&""!==t.path||(t.path=this._getCurrentPath()),_.isString(t.path)&&(-1===t.path.indexOf("?")?t.path=t.path+"?":"&"!==t.path.slice(-1)&&(t.path=t.path+"&"),t.path=t.path+"tdshare="+encodeURIComponent(this._generateShareInfo()))),t}},{key:"onPageShow",value:function(){var e,t;this.config.pageLeave&&this.taInstance.timeEvent("ta_page_leave"),this.config.pageShow&&(e=this._getCurrentPath(),t={},this.disablePresetList.includes("#url_path")||(t["#url_path"]=e||"The system did not get a value"),this.disablePresetList.includes("#referrer")||(t["#referrer"]=this.referrer),_.extend(t,this.config.properties),_.isFunction(this.config.callback)&&_.extend(t,this.config.callback("pageShow")),this.referrer=e,this.taInstance._internalTrack("ta_mp_view",t))}},{key:"onPageUnload",value:function(){var e,t;this.config.pageLeave&&(e=this._getCurrentPath(),t={},this.disablePresetList.includes("#url_path")||(t["#url_path"]=e||"The system did not get a value"),_.extend(t,this.config.properties),_.isFunction(this.config.callback)&&_.extend(t,this.config.callback("pageLeave")),this.taInstance._internalTrack("ta_page_leave",t))}},{key:"onPageAddToFavorites",value:function(){var e,t;this.config.mpFavorite&&(e=this._getCurrentPath(),t={},this.disablePresetList.includes("#url_path")||(t["#url_path"]=e||"The system did not get a value"),_.extend(t,this.config.properties),_.isFunction(this.config.callback)&&_.extend(t,this.config.callback("mpFavorite")),this.taInstance._internalTrack("ta_add_favorite",t))}}]),i}(),AutoTrackBridge$1=function(){function a(e,t,i){var n=this;_classCallCheck(this,a),this.taInstance=e,this.config=t.autoTrack||{},this.disablePresetList=t.disablePresetProperties||[];t=i.getLaunchOptionsSync();this._onLaunch(t),this._onShow(t),this.startTracked=!0,i.onShow(function(e){n._onShow(e)}),i.onHide(function(){var e;n.startTracked=!1,n.config.appHide&&(e={},_.extend(e,n.config.properties),_.isFunction(n.config.callback)&&_.extend(e,n.config.callback("appHide")),n.taInstance._internalTrack("ta_mg_hide",e),n.taInstance.flush())})}return _createClass(a,[{key:"_onLaunch",value:function(e){var t;this.disablePresetList.includes("#scene")||e&&e.scene&&this.taInstance._setAutoTrackProperties({"#scene":e.scene}),this.config.appLaunch&&(t={},_.extend(t,this.config.properties),_.isFunction(this.config.callback)&&_.extend(t,this.config.callback("appLaunch")),this.disablePresetList.includes("#start_reason")||e&&(t["#start_reason"]=JSON.stringify(e)),this.taInstance._internalTrack("ta_mg_launch",t))}},{key:"_onShow",value:function(e){var t;this.startTracked||(this.config.appHide&&this.taInstance.timeEvent("ta_mg_hide"),this.config.appShow&&(t={},_.extend(t,this.config.properties),_.isFunction(this.config.callback)&&_.extend(t,this.config.callback("appShow")),this.disablePresetList.includes("#start_reason")||e&&(t["#start_reason"]=JSON.stringify(e)),this.taInstance._internalTrack("ta_mg_show",t)))}}]),a}(),PlatformProxy$1=function(){function n(e,t,i){_classCallCheck(this,n),this.api=e,this.config=t,this._config=i}return _createClass(n,[{key:"getConfig",value:function(){return this.config}},{key:"initSdkConfig",value:function(){}},{key:"getStorage",value:function(e,t,i){if(t)this.api.getStorage({key:e,success:function(e){e=_.isJSONString(e.data)?JSON.parse(e.data):{};i(e)},fail:function(){logger$1.warn("getStorage faild"),i({})}});else try{if("dd_mp"===this._config.platform||"ali_mp"===this._config.platform||"ali_mg"===this._config.platform){var n=this.api.getStorageSync({key:e});return _.isJSONString(n.data)?JSON.parse(n.data):{}}n=this.api.getStorageSync(e);return _.isJSONString(n)?JSON.parse(n):{}}catch(e){return{}}}},{key:"setStorage",value:function(e,t){try{"ali_mp"===this._config.platform||"tb_mp"===this._config.platform||"dd_mp"===this._config.platform||"ali_mg"===this._config.platform?this.api.setStorageSync({key:e,data:t}):this.api.setStorageSync(e,t)}catch(e){}}},{key:"removeStorage",value:function(e){try{_.isFunction(this.api.removeStorage)?this.api.removeStorage({key:e}):_.isFunction(this.api.deleteStorage)&&this.api.deleteStorage({key:e})}catch(e){}}},{key:"_getPlatform",value:function(){return""}},{key:"getSystemInfo",value:function(i){var n=this._config.mpPlatform,a=this;this.api.getSystemInfo({success:function(e){var t;_.isFunction(n)?e.mp_platform=n(e):e.mp_platform=n,"ali_mp"!==a._config.platform&&"ali_mg"!==a._config.platform||(e.system=e.platform+" "+e.system),"wechat_mp"===a._config.platform||"wechat_mg"===a._config.platform?(t=a.api.getAccountInfoSync(),e.appVersion=t.miniProgram.version):"tt_mg"!==a._config.platform&&"tt_mg"!==a._config.platform||(e.appVersion=a.api.getEnvInfoSync().microapp.mpVersion),i.success(e),"wechat"===n&&i.complete()},complete:function(){i.complete()}})}},{key:"getNetworkType",value:function(t){_.isFunction(this.api.getNetworkType)?this.api.getNetworkType({success:function(e){t.success(e)},complete:function(){t.complete()}}):(t.success({}),t.complete())}},{key:"onNetworkStatusChange",value:function(e){_.isFunction(this.api.onNetworkStatusChange)?this.api.onNetworkStatusChange(e):e({})}},{key:"request",value:function(t){if("ali_mp"!==this._config.platform&&"dd_mp"!==this._config.platform&&"ali_mg"!==this._config.platform)return this.api.request(t);var e=_.extend({},t);return e.headers=t.header,e.header=void 0,e.success=function(e){e.statusCode=e.status,t.success(e)},e.fail=function(e){e.errMsg=e.errorMessage,t.fail(e)},"dd_mp"===this._config.platform?this.api.httpRequest(e):this.api.request(e)}},{key:"initAutoTrackInstance",value:function(e,t){return _.isObject(t.autoTrack)&&(t.autoTrack.isPlugin=t.is_plugin),new(this._config.mp?AutoTrackBridge:AutoTrackBridge$1)(e,t,this.api)}},{key:"setGlobal",value:function(e,t){this._config.mp?logger$1.warn("ThinkingAnalytics: we do not set global name for TA instance when you do not enable auto track."):"ali_mg"!==this._config.platform&&(GameGlobal[t]=e)}},{key:"getAppOptions",value:function(e){var t={};try{t=this.api.getLaunchOptionsSync()}catch(e){logger$1.warn("Cannot get launch options.")}if(_.isFunction(e))try{this._config.mp?this.api.onAppShow(e):this.api.onShow(e)}catch(e){logger$1.warn("Cannot register onShow callback.")}return t}},{key:"showToast",value:function(e){var t;_.isFunction(this.api.showToast)&&(t={title:e},"dd_mp"!==this._config.platform&&"ali_mp"!==this._config.platform||(t.content=e),this.api.showToast(t))}},{key:"setGlobalData",value:function(e){"wechat_mg"===this._config.platform?GameGlobal&&(GameGlobal.tdanalytics2024=e):"ali_mp"===this._config.platform?global.tdanalytics2024=e:"tt_mp"===this._config.platform||"kuaishou_mp"===this._config.platform?this.api.tdanalytics2024=e:globalThis.tdanalytics2024=e}}],[{key:"createInstance",value:function(){return this._createInstance("R_CURRENT_PLATFORM")}},{key:"_createInstance",value:function(e){switch(e){case"wechat_mp":return new n(wx,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_wechat",plat:"wx"},{mpPlatform:"wechat",mp:!0,platform:e});case"wechat_mg":return new n(wx,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_wechat_game",plat:"wx"},{mpPlatform:"wechat",platform:e});case"qq_mp":return new n(qq,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qq"},{mpPlatform:"qq",mp:!0,platform:e});case"qq_mg":return new n(qq,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qq_game"},{mpPlatform:"qq",platform:e});case"baidu_mp":return new n(swan,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_swan"},{mpPlatform:function(e){return e.host},mp:!0,platform:e});case"baidu_mg":return new n(swan,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_swan_game"},{mpPlatform:function(e){return e.host},platform:e});case"tt_mg":return new n(tt,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_tt_game"},{mpPlatform:function(e){return e.appName},platform:e});case"tt_mp":return new n(tt,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_tt"},{mpPlatform:function(e){return e.appName},mp:!0,platform:e});case"ali_mp":return new n(my,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_ali"},{mpPlatform:function(e){return e.app},mp:!0,platform:e});case"ali_mg":return new n(my,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_ali_game"},{mpPlatform:function(e){return e.app},platform:e});case"dd_mp":return new n(dd,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_dd"},{mpPlatform:"dingding",mp:!0,platform:e});case"bl_mg":return new n(bl,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_mg"},{mpPlatform:"bilibili",platform:e});case"kuaishou_mp":return new n(ks,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_kuaishou"},{mpPlatform:"kuaishou",mp:!0,platform:e});case"kuaishou_mg":return new n(ks,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_kuaishou"},{mpPlatform:"kuaishou",platform:e});case"qh360_mg":return new n(qh,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qh360"},{mpPlatform:"qh360",platform:e});case"tb_mp":return new n(my,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_tb"},{mpPlatform:"tb",mp:!0,platform:e});case"jd_mp":return new n(jd,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_jd"},{mpPlatform:"jd",mp:!0,platform:e});case"qh360_mp":return new n(qh,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qh360"},{mpPlatform:"qh360",mp:!0,platform:e});case"WEB":return new PlatformProxy.createInstance}}}]),n}(),AutoTrackBridge$2=function e(t,i){var n=this;_classCallCheck(this,e),this.taInstance=t,this.config=i||{},this.config.appShow&&this.taInstance._internalTrack("ta_mg_show"),this.config.appHide&&this.taInstance.timeEvent("ta_mg_hide"),qg.onShow(function(){var e;n.config.appHide&&n.taInstance.timeEvent("ta_mg_hide"),n.config.appShow&&(e={},_.extend(e,n.config.properties),_.isFunction(n.config.callback)&&_.extend(e,n.config.callback("appShow")),n.taInstance._internalTrack("ta_mg_show"))}),qg.onHide(function(){var e;n.config.appHide&&(e={},_.extend(e,n.config.properties),_.isFunction(n.config.callback)&&_.extend(e,n.config.callback("appHide")),n.taInstance._internalTrack("ta_mg_hide"))})},PlatformProxy$2=function(){function e(){_classCallCheck(this,e),this.config={persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qg_vivo_game",asyncPersistence:!0}}return _createClass(e,[{key:"getConfig",value:function(){return this.config||{}}},{key:"initSdkConfig",value:function(){}},{key:"getStorage",value:function(e,t,i){if(!t){t=qg.getStorageSync({key:e});return _.isJSONString(t)?JSON.parse(t):{}}qg.getStorage({key:e,success:function(e){e=_.isJSONString(e)?JSON.parse(e):{};i(e)},fail:function(){i({})}})}},{key:"setStorage",value:function(e,t){qg.setStorage({key:e,value:t})}},{key:"removeStorage",value:function(e){qg.deleteStorage({key:e})}},{key:"getSystemInfo",value:function(n){qg.getSystemInfo({success:function(e){var t=e,i=[e.osType,e.osVersionName].join(" ");t.brand=e.manufacturer,t.system=i,t.mp_platform="vivo_qg",n.success(t)},complete:function(){n.complete()}})}},{key:"getNetworkType",value:function(i){qg.getNetworkType({success:function(e){var t=e;t.networkType=e.type,i.success(t)},complete:function(){i.complete()}})}},{key:"onNetworkStatusChange",value:function(i){qg.subscribeNetworkStatus({callback:function(e){var t=e;t.networkType=e.type,i(t)}})}},{key:"request",value:function(t){return qg.request({url:t.url,data:t.data,method:t.method,header:t.header,success:function(e){t.success(e)},fail:function(e){t.fail(e)}})}},{key:"initAutoTrackInstance",value:function(e,t){return new AutoTrackBridge$2(e,t.autoTrack)}},{key:"setGlobal",value:function(e,t){globalThis[t]=e}},{key:"getAppOptions",value:function(){return{}}},{key:"showToast",value:function(e){qg.showToast({message:e,duration:0})}},{key:"setGlobalData",value:function(){}}],[{key:"createInstance",value:function(){return new e}}]),e}(),AutoTrackBridge$3=function e(t,i,n){var a=this;_classCallCheck(this,e),this.taInstance=t,this.config=i||{},this.config.appShow&&(i={},_.extend(i,this.config.properties),_.isFunction(this.config.callback)&&_.extend(i,this.config.callback("appShow")),this.taInstance._internalTrack("ta_mg_show",i)),this.config.appHide&&this.taInstance.timeEvent("ta_mg_hide"),n.onShow(function(){var e;a.config.appHide&&a.taInstance.timeEvent("ta_mg_hide"),a.config.appShow&&(e={},_.extend(e,a.config.properties),_.isFunction(a.config.callback)&&_.extend(e,a.config.callback("appShow")),a.taInstance._internalTrack("ta_mg_show",e))}),n.onHide(function(){var e;a.config.appHide&&(e={},_.extend(e,a.config.properties),_.isFunction(a.config.callback)&&_.extend(e,a.config.callback("appHide")),a.taInstance._internalTrack("ta_mg_hide",e))})},PlatformProxy$3=function(){function n(e,t,i){_classCallCheck(this,n),this.api=e,this.config=t,this._config=i}return _createClass(n,[{key:"getConfig",value:function(){return this.config||{}}},{key:"initSdkConfig",value:function(){}},{key:"getStorage",value:function(e,t,i){e=localStorage.getItem(e);if(!t)return _.isJSONString(e)?JSON.parse(e):{};_.isJSONString(e)?i(JSON.parse(e)):i({})}},{key:"setStorage",value:function(e,t){localStorage.setItem(e,t)}},{key:"removeStorage",value:function(e){localStorage.removeItem(e)}},{key:"getSystemInfo",value:function(t){var i=this._config.mpPlatform;this.api.getSystemInfo({success:function(e){e.mp_platform=i,t.success(e)},complete:function(){t.complete()}})}},{key:"getNetworkType",value:function(t){this.api.getNetworkType({success:function(e){t.success(e)},complete:function(){t.complete()}})}},{key:"onNetworkStatusChange",value:function(t){this.api.onNetworkStatusChange({callback:function(e){t(e)}})}},{key:"request",value:function(e){var t={},i=new XMLHttpRequest;if(i.open(e.method,e.url),e.header)for(var n in e.header)i.setRequestHeader(n,e.header[n]);return i.onreadystatechange=function(){4===i.readyState&&200===i.status?(t.statusCode=200,_.isJSONString(i.responseText)&&(t.data=JSON.parse(i.responseText)),e.success(t)):200!==i.status&&(t.errMsg="network error",e.fail(t))},i.ontimeout=function(){t.errMsg="timeout",e.fail(t)},i.send(e.data),i}},{key:"initAutoTrackInstance",value:function(e,t){return new AutoTrackBridge$3(e,t.autoTrack,this.api)}},{key:"setGlobal",value:function(e,t){globalThis[t]=e}},{key:"getAppOptions",value:function(){return this.api.getLaunchOptionsSync()}},{key:"showToast",value:function(e){this.api.showToast({title:e,icon:"none",duration:2e3})}},{key:"setGlobalData",value:function(){}}],[{key:"createInstance",value:function(){return this._createInstance("R_CURRENT_PLATFORM")}},{key:"_createInstance",value:function(e){switch(e){case"oppo":return new n(qg,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qg_oppo_game"},{mpPlatform:"oppo_qg"});case"huawei":return new n(hbs,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qg_huawei_game"},{mpPlatform:"huawei_qg"});case"mz":return new n(qg,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qg_mz_game"},{mpPlatform:"mz"});case"xiaomi":return new n(qg,{persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_qg"},{mpPlatform:"xiaomi"})}}}]),n}(),PlatformProxyCC=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"createInstance",value:function(){var e=Object.freeze({WECHAT_GAME:104,QQ_PLAY:105,BAIDU_GAME:107,VIVO_GAME:108,OPPO_GAME:109,HUAWEI_GAME:110,XIAOMI_GAME:111,BYTEDANCE_GAME:117,QTT_GAME:116,LINKSURE:119,ALI_GAME:113,WECHAT_MINI_GAME:"WECHAT_GAME",BAIDU_MINI_GAME:"BAIDU_MINI_GAME",XIAOMI_QUICK_GAME:"XIAOMI_QUICK_GAME",OPPO_MINI_GAME:"OPPO_MINI_GAME",VIVO_MINI_GAME:"VIVO_MINI_GAME",HUAWEI_QUICK_GAME:"HUAWEI_QUICK_GAME",BYTEDANCE_MINI_GAME:"BYTEDANCE_MINI_GAME",QTT_MINI_GAME:"QTT_MINI_GAME",LINKSURE_MINI_GAME:"LINKSURE_MINI_GAME",ALIPAY_MINI_GAME:"ALIPAY_MINI_GAME"});if(cc.sys.platform===e.WECHAT_GAME||cc.sys.platform===e.WECHAT_MINI_GAME)return PlatformProxy$1._createInstance("wechat_mg");if(cc.sys.platform===e.BAIDU_GAME||cc.sys.platform===e.BAIDU_MIN_GAME)return PlatformProxy$1._createInstance("baidu_mg");if(cc.sys.platform===e.VIVO_GAME||cc.sys.platform===e.VIVO_MINI_GAME)return PlatformProxy$2.createInstance();if(cc.sys.platform===e.QQ_PLAY)return PlatformProxy$1._createInstance("qq_mg");if(cc.sys.platform===e.OPPO_GAME||cc.sys.platform===e.OPPO_MINI_GAME)return PlatformProxy$3._createInstance("oppo");if(cc.sys.platform===e.HUAWEI_GAME||cc.sys.platform===e.HUAWEI_QUICK_GAME)return PlatformProxy$3._createInstance("huawei");if(cc.sys.platform===e.XIAOMI_GAME||cc.sys.platform===e.XIAOMI_QUICK_GAME)return PlatformProxy$3._createInstance("xiaomi");if(cc.sys.platform===e.BYTEDANCE_GAME||cc.sys.platform===e.BYTEDANCE_MINI_GAME)return PlatformProxy$1._createInstance("tt_mg");if(cc.sys.platform===e.ALI_GAME||cc.sys.platform===e.ALIPAY_MINI_GAME)return PlatformProxy$1._createInstance("ali_mg");var i=PlatformProxy.createInstance();return i._sysCallback=function(){return{system:cc.sys.os.replace(" ","")+" "+cc.sys.osVersion}},i.getNetworkType=function(e){var t={};switch(cc.sys.getNetworkType()){case cc.sys.NetworkType.LAN:t.networkType="WIFI";break;case cc.sys.NetworkType.WWAN:t.networkType="WWAN";break;default:t.networkType="NONE"}e.success(t),e.complete()},i.getSystemInfo=function(e){var t={mp_platform:cc.sys.platform.toString(),system:i._getOs(),screenWidth:window.screen.width,screenHeight:window.screen.height};i._sysCallback&&(t=_.extend(t,i._sysCallback(e))),e.success(t),e.complete()},i}}]),e}(),PlatformAPI=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"_getCurrentPlatform",value:function(){return this.currentPlatform||(this.currentPlatform=PlatformProxyCC.createInstance())}},{key:"getConfig",value:function(){return this._getCurrentPlatform().getConfig()}},{key:"initConfig",value:function(e){this._getCurrentPlatform().initSdkConfig(e)}},{key:"isWxPlat",value:function(){return"wx"===this.getConfig().plat}},{key:"getStorage",value:function(e,t,i){return this._getCurrentPlatform().getStorage(e,t,i)}},{key:"setStorage",value:function(e,t){return this._getCurrentPlatform().setStorage(e,t)}},{key:"removeStorage",value:function(e){return this._getCurrentPlatform().removeStorage(e)}},{key:"getSystemInfo",value:function(e){return this._getCurrentPlatform().getSystemInfo(e)}},{key:"getNetworkType",value:function(e){return this._getCurrentPlatform().getNetworkType(e)}},{key:"onNetworkStatusChange",value:function(e){this._getCurrentPlatform().onNetworkStatusChange(e)}},{key:"request",value:function(e){return this._getCurrentPlatform().request(e)}},{key:"initAutoTrackInstance",value:function(e,t){return this._getCurrentPlatform().initAutoTrackInstance(e,t)}},{key:"setGlobal",value:function(e,t){e&&t&&this._getCurrentPlatform().setGlobal(e,t)}},{key:"getAppOptions",value:function(e){return this._getCurrentPlatform().getAppOptions(e)}},{key:"showDebugToast",value:function(e){this._getCurrentPlatform().showToast(e)}},{key:"setGlobalData",value:function(e){this._getCurrentPlatform().setGlobalData(e)}}]),e}(),KEY_NAME_MATCH_REGEX=/^[a-zA-Z][a-zA-Z0-9_]{0,49}$/,PropertyChecker=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"stripProperties",value:function(e){return _.isObject(e)&&_.each(e,function(e,t){_.isString(e)||_.isNumber(e)||_.isDate(e)||_.isBoolean(e)||_.isArray(e)||_.isObject(e)||logger$1.warn("Your data -",t,e,"- format does not meet requirements and may not be stored correctly. Attribute values only support String, Number, Date, Boolean, Array, Object")}),e}},{key:"_checkPropertiesKey",value:function(e){var i=!0;return _.each(e,function(e,t){KEY_NAME_MATCH_REGEX.test(t)||(logger$1.warn("Invalid KEY: "+t),i=!1)}),i}},{key:"event",value:function(e){return!(!_.isString(e)||!KEY_NAME_MATCH_REGEX.test(e))||(logger$1.warn("Check the parameter format. The eventName must start with an English letter and contain no more than 50 characters including letters, digits, and underscores: "+e),!1)}},{key:"propertyName",value:function(e){return!(!_.isString(e)||!KEY_NAME_MATCH_REGEX.test(e))||(logger$1.warn("Check the parameter format. PropertyName must start with a letter and contain letters, digits, and underscores (_). The value is a string of no more than 50 characters: "+e),!1)}},{key:"properties",value:function(e){return this.stripProperties(e),!e||(_.isObject(e)?!!this._checkPropertiesKey(e)||(logger$1.warn("Check the parameter format. The properties key must start with a letter, contain digits, letters, and underscores (_), and contain a maximum of 50 characters"),!1):(logger$1.warn("properties can be none, but it must be an object"),!1))}},{key:"propertiesMust",value:function(e){return this.stripProperties(e),void 0===e||!_.isObject(e)||_.isEmptyObject(e)?(logger$1.warn("properties must be an object with a value"),!1):!!this._checkPropertiesKey(e)||(logger$1.warn("Check the parameter format. The properties key must start with a letter, contain digits, letters, and underscores (_), and contain a maximum of 50 characters"),!1)}},{key:"userId",value:function(e){return!(!_.isString(e)||!/^.{1,64}$/.test(e))||(logger$1.warn("The user ID must be a string of less than 64 characters and cannot be null"),!1)}},{key:"userAddProperties",value:function(e){if(!this.propertiesMust(e))return!1;for(var t in e)if(!_.isNumber(e[t]))return logger$1.warn("The attributes of userAdd need to be Number"),!1;return!0}},{key:"userAppendProperties",value:function(e){if(!this.propertiesMust(e))return!1;for(var t in e)if(!_.isArray(e[t]))return logger$1.warn("The attribute of userAppend must be Array"),!1;return!0}}]),e}(),HttpTask=function(){function r(e,t,i,n,a){_classCallCheck(this,r),this.data=e,this.serverUrl=t,this.callback=a,this.tryCount=_.isNumber(i)?i:1,this.timeout=_.isNumber(n)?n:3e3,this.taClassName="HttpTask"}return _createClass(r,[{key:"run",value:function(){var t=this,e=_.createExtraHeaders();e["content-type"]="application/json",this.runTime=_.getCurrentTimeStamp(),PlatformAPI.request({url:this.serverUrl,method:"POST",data:this.data,header:e,success:function(e){t.onSuccess(e)},fail:function(e){t.onFailed(e)}})}},{key:"onSuccess",value:function(e){if(!this.sendTimeout())if(_.isObject(e)&&200===e.statusCode){var t;switch((_.isUndefined(e.data)||_.isUndefined(e.data.code))&&(e.data={code:0}),e.data.code){case 0:t="success";break;case-1:t="invalid data";break;case-2:t="invalid APP ID";break;default:t="Unknown return code"}this.callback({code:e.data.code,msg:t})}else this.callback({code:-3,msg:_.isObject(e)?e.statusCode:"Unknown error"})}},{key:"onFailed",value:function(e){this.sendTimeout()||(0<--this.tryCount?this.run():this.callback({code:-3,msg:_.isObject(e)?e.errMsg:"Unknown error"}))}},{key:"sendTimeout",value:function(){return _.getCurrentTimeStamp()-this.runTime>this.timeout}}]),r}(),HttpTaskDebug=function(){function o(e,t,i,n,a,r,s){_classCallCheck(this,o),this.data=e,this.serverDebugUrl=t,this.callback=s,this.tryCount=_.isNumber(i)?i:1,this.timeout=_.isNumber(n)?n:3e3,this.dryrun=a,this.deviceId=r,this.taClassName="HttpTaskDebug"}return _createClass(o,[{key:"run",value:function(){var t=this,e="appid="+this.data["#app_id"]+"&source=client&dryRun="+this.dryrun+"&deviceId="+this.deviceId+"&data="+encodeURIComponent(JSON.stringify(this.data.data[0])),i=_.createExtraHeaders();i["content-type"]="application/x-www-form-urlencoded";var n=PlatformAPI.request({url:this.serverDebugUrl,method:"POST",data:e,header:i,success:function(e){t.onSuccess(e),clearTimeout(a)},fail:function(e){t.onFailed(e),clearTimeout(a)}}),a=setTimeout(function(){(_.isObject(n)||_.isPromise(n))&&_.isFunction(n.abort)&&n.abort()},this.timeout)}},{key:"onSuccess",value:function(e){if(_.isObject(e)&&200===e.statusCode){var t;if((_.isUndefined(e.data)||_.isUndefined(e.data.errorLevel))&&(e.data={errorLevel:0}),0===e.data.errorLevel)t="Verify data success.";else if(1===e.data.errorLevel){for(var i=e.data.errorProperties,n="",a=0;a<i.length;a++)var r=i[a].errorReason,n=n+" propertyName:"+i[a].propertyName+" errorReasons:"+r+"\n";t="Debug data error. errorLevel:"+e.data.errorLevel+" reason:"+n}else 2!==e.data.errorLevel&&-1!==e.data.errorLevel||(t="Debug data error. errorLevel:"+e.data.errorLevel+" reason:"+e.data.errorReasons);logger$1.info(t),this.callback({code:e.data.errorLevel,msg:t})}else this.callback({code:-3,msg:_.isObject(e)?e.statusCode:"Unknown error"})}},{key:"onFailed",value:function(e){0<--this.tryCount?this.run():this.callback({code:-3,msg:_.isObject(e)?e.errMsg:"Unknown error"})}}]),o}(),SenderQueue=function(){function e(){_classCallCheck(this,e),this.items=[],this.isRunning=!1,this.showDebug=!1}return _createClass(e,[{key:"enqueue",value:function(e,t,i,n){var a=this,n=!(3<arguments.length&&void 0!==n)||n,r=this,t="debug"===i.debugMode?new HttpTaskDebug(e,t,i.maxRetries,i.sendTimeout,0,i.deviceId,function(e){r.isRunning=!1,delete a.runTime,_.isFunction(i.callback)&&i.callback(e),r._runNext(),!1===r.showDebug&&(0!==e.code&&1!==e.code&&2!==e.code||(r.showDebug=!0,_.isFunction(PlatformAPI.showDebugToast)&&PlatformAPI.showDebugToast("The current mode is Debug")))}):"debugOnly"===i.debugMode?new HttpTaskDebug(e,t,i.maxRetries,i.sendTimeout,1,i.deviceId,function(e){r.isRunning=!1,delete a.runTime,_.isFunction(i.callback)&&i.callback(e),r._runNext(),!1===r.showDebug&&(0!==e.code&&1!==e.code&&2!==e.code||(r.showDebug=!0,_.isFunction(PlatformAPI.showDebugToast)&&PlatformAPI.showDebugToast("The current mode is debugOnly")))}):new HttpTask(JSON.stringify(e),t,i.maxRetries,i.sendTimeout,function(e){r.isRunning=!1,delete a.runTime,_.isFunction(i.callback)&&i.callback(e),r._runNext()});!0===n?(this.items.push(t),this._runNext()):t.run()}},{key:"_dequeue",value:function(){return this.items.shift()}},{key:"_runNext",value:function(){if(0<this.items.length&&!this.isRunning)if(this.isRunning=!0,this.runTime=_.getCurrentDate(),"HttpTask"!==this.items[0].taClassName)this._dequeue().run();else{var e=this.items.splice(0,this.items.length),t=e[0],i=JSON.parse(t.data),n=i["#app_id"],a=[];a.push(t.callback);for(var r=1;r<e.length;r++){var s=e[r],o=JSON.parse(s.data);o["#app_id"]===n&&t.serverUrl===s.serverUrl?(i.data=i.data.concat(o.data),a.push(s.callback)):this.items.push(s)}var c=_.getCurrentTimeStamp();i["#flush_time"]=c,new HttpTask(JSON.stringify(i),t.serverUrl,t.tryCount,t.timeout,function(e){for(var t in a)Object.hasOwnProperty.call(a,t)&&(0,a[t])(e)}).run()}}},{key:"runTimeout",value:function(e){if(_.isDate(this.runTime)&&_.getCurrentDate().getTime()-this.runTime.getTime()>e)return!0;return!1}},{key:"resetTimeout",value:function(){this.isRunning=!1,delete this.runTime}}]),e}(),senderQueue=new SenderQueue,DEFAULT_CONFIG={name:"thinkingdata",is_plugin:!1,maxRetries:3,sendTimeout:3e3,enablePersistence:!0,asyncPersistence:!1,enableLog:!0,strict:!1,debugMode:"none",enableCalibrationTime:!1,enableBatch:!1,disablePresetProperties:[],cloudEnv:"online",reportingToTencentSdk:3},systemInformation={properties:{},disableList:[],initDisableList:function(e){this.disableList=e,this.disableList.includes("#lib")||(this.properties["#lib"]=Config.LIB_NAME),this.disableList.includes("#lib_version")||(this.properties["#lib_version"]=Config.LIB_VERSION)},initDeviceId:function(e){_.isString(e)&&(this.disableList.includes("#device_id")||(this.properties["#device_id"]=e))},getSystemInfo:function(e){var n=this;PlatformAPI.onNetworkStatusChange(function(e){n.disableList.includes("#network_type")||(n.properties["#network_type"]=e.networkType)}),PlatformAPI.getNetworkType({success:function(e){n.disableList.includes("#network_type")||(n.properties["#network_type"]=e.networkType)},complete:function(){PlatformAPI.getSystemInfo({success:function(e){var t=e.system?e.system.replace(/\s+/g," ").split(" "):[],i={};n.disableList.includes("#manufacturer")||(i["#manufacturer"]=e.brand),n.disableList.includes("#device_model")||(i["#device_model"]=e.model),n.disableList.includes("#screen_width")||(i["#screen_width"]=Number(e.screenWidth)),n.disableList.includes("#screen_height")||(i["#screen_height"]=Number(e.screenHeight)),n.disableList.includes("#os")||(i["#os"]=t[0]),n.disableList.includes("#os_version")||(i["#os_version"]=t[1]),n.disableList.includes("#mp_platform")||(i["#mp_platform"]=e.mp_platform),n.disableList.includes("#system_language")||(i["#system_language"]=e.systemLanguage),n.disableList.includes("#app_version")||(i["#app_version"]=e.appVersion),_.extend(n.properties,i),_.setMpPlatform(e.mp_platform)},complete:function(){e()}})}})}},ThinkingDataPersistence=function(){function e(t,i){var n=this;_classCallCheck(this,e),this.enabled=t.enablePersistence,this.enabled?(t.isChildInstance?(this.name=t.persistenceName+"_"+t.name,this.nameOld=t.persistenceNameOld+"_"+t.name):(this.name=t.persistenceName,this.nameOld=t.persistenceNameOld),t.asyncPersistence?(this._state={},PlatformAPI.getStorage(this.name,!0,function(e){_.isEmptyObject(e)?PlatformAPI.getStorage(n.nameOld,!0,function(e){n._state=_.extend2Layers({},e,n._state),n._init(t,i),n._save()}):(n._state=_.extend2Layers({},e,n._state),n._init(t,i),n._save())})):(this._state=PlatformAPI.getStorage(this.name)||{},_.isEmptyObject(this._state)&&(this._state=PlatformAPI.getStorage(this.nameOld)||{}),this._init(t,i))):(this._state={},this._init(t,i))}return _createClass(e,[{key:"_init",value:function(e,t){this.getDistinctId()||this.setDistinctId(_.UUID()),e.isChildInstance||(this.getDeviceId()||this._setDeviceId(_.UUID()),systemInformation.initDeviceId(this.getDeviceId())),this.initComplete=!0,"function"==typeof t&&t(),this._save()}},{key:"_save",value:function(){this.enabled&&this.initComplete&&PlatformAPI.setStorage(this.name,JSON.stringify(this._state))}},{key:"_set",value:function(e,t){var i,n=this;"string"==typeof e?(i={})[e]=t:"object"===_typeof(e)&&(i=e),_.each(i,function(e,t){n._state[t]=e}),this._save()}},{key:"_get",value:function(e){return this._state[e]}},{key:"setEventTimer",value:function(e,t){var i=this._state.event_timers||{};i[e]=t,this._set("event_timers",i)}},{key:"removeEventTimer",value:function(e){var t=(this._state.event_timers||{})[e];return _.isUndefined(t)||(delete this._state.event_timers[e],this._save()),t}},{key:"getDeviceId",value:function(){return this._state.device_id}},{key:"_setDeviceId",value:function(e){this.getDeviceId()?logger$1.warn("cannot modify the device id."):this._set("device_id",e)}},{key:"getDistinctId",value:function(){return this._state.distinct_id}},{key:"setDistinctId",value:function(e){this._set("distinct_id",e)}},{key:"getAccountId",value:function(){return this._state.account_id}},{key:"setAccountId",value:function(e){this._set("account_id",e)}},{key:"getSuperProperties",value:function(){return this._state.props||{}}},{key:"setSuperProperties",value:function(e,t){e=t?e:_.extend(this.getSuperProperties(),e);this._set("props",e)}}]),e}(),dataStoragePrefix="ta_mpsdk_",tabStoragePrefix="tab_tampsdk_",BatchConsumer=function(){function r(e,t){_classCallCheck(this,r),this.config=e,this.ta=t,this.timer=null,this.batchConfig=_.extend({size:6,interval:6e3,maxLimit:500},this.config.batchConfig),this.batchConfig.size<1&&(this.batchConfig.size=1),30<this.batchConfig.size&&(this.batchConfig.size=30),this.storageKey=dataStoragePrefix+this.config.appId,this.maxLimit=this.batchConfig.maxLimit,this.batchList=[];t=PlatformAPI.getStorage(this.storageKey);_.isArray(t)&&(this.batchList=t);var t=tabStoragePrefix+this.config.appId,i=PlatformAPI.getStorage(t);if(_.isArray(i)){for(var n=0;n<i.length;n++){var a=PlatformAPI.getStorage(i[n]);this.batchList.push(a),PlatformAPI.removeStorage(i[n])}PlatformAPI.removeStorage(t)}this.dataHasChange=!1,this.dataSendTimeStamp=0}return _createClass(r,[{key:"batchInterval",value:function(){this.loopWrite(),this.loopSend()}},{key:"loopWrite",value:function(){var e=this;setTimeout(function(){e.batchWrite(),e.loopWrite()},500)}},{key:"batchWrite",value:function(){this.dataHasChange&&(this.dataHasChange=!1,PlatformAPI.setStorage(this.storageKey,JSON.stringify(this.batchList)))}},{key:"loopSend",value:function(){var e=this;e.timer=setTimeout(function(){e.batchSend(),clearTimeout(e.timer),e.loopSend()},this.batchConfig.interval)}},{key:"add",value:function(e){this.batchList.length>this.maxLimit&&this.batchList.shift(),this.batchList.push(e),this.dataHasChange=!0,this.batchList.length>this.batchConfig.size&&this.batchSend()}},{key:"flush",value:function(){clearTimeout(this.timer),this.batchSend(),this.loopSend()}},{key:"batchSend",value:function(){var t,i,n,e=_.getCurrentTimeStamp();0!==this.dataSendTimeStamp&&e-this.dataSendTimeStamp<this.config.sendTimeout+500||(this.dataSendTimeStamp=_.getCurrentTimeStamp(),0<(t=(e=30<this.batchList.length?this.batchList.slice(0,30):this.batchList).length)&&((i={}).data=e,i["#app_id"]=this.config.appId,i["#flush_time"]=_.getCurrentTimeStamp(),n=this,senderQueue.enqueue(i,this.ta.serverUrl,{maxRetries:1,sendTimeout:this.config.sendTimeout,callback:function(e){0===e.code&&(logger$1.info("Flush success: "+JSON.stringify(i,null,4)),n.batchRemove(t))},debugMode:this.config.debugMode,deviceId:this.ta.getDeviceId()},!1)))}},{key:"batchRemove",value:function(e){this.dataSendTimeStamp=0,this.batchList.splice(0,e),this.dataHasChange=!0,this.batchWrite()}}]),r}(),ThinkingDataAPI=function(){function i(e){if(_classCallCheck(this,i),e){if(!PlatformAPI.isWxPlat()||1!==e.reportingToTencentSdk&&2!==e.reportingToTencentSdk||(t=e.tgaInitParams.tgaSDK,e.tgaInitParams&&t&&("debug"!==e.debugMode&&"debugOnly"!==e.debugMode||t.setDebug(!0),this.wxSdk=new t({user_action_set_id:e.tgaInitParams.user_action_set_id,secret_key:e.tgaInitParams.secret_key,appid:e.tgaInitParams.appid}),e.tgaInitParams.openId?this.wxSdk.setOpenId(e.tgaInitParams.openId):e.tgaInitParams.unionId&&this.wxSdk.setUnionId(e.tgaInitParams.unionId))),this.isTADisable=1===e.reportingToTencentSdk,e.appId=e.appId?_.checkAppId(e.appId):_.checkAppId(e.appid),e.serverUrl=e.serverUrl?_.checkUrl(e.serverUrl):_.checkUrl(e.server_url),!e.appId||!e.serverUrl)throw new Error("appId or serverUrl can not be empty");var t=_.extend({},DEFAULT_CONFIG,PlatformAPI.getConfig());_.isObject(e)?this.config=_.extend(t,e):this.config=t,this._init(this.config)}}return _createClass(i,[{key:"_init",value:function(e){var t=this;this.name=e.name,this.appId=e.appId||e.appid;var i=e.serverUrl||e.server_url;this.serverUrl=i+"/sync_xcx",this.serverDebugUrl=i+"/data_debug",this.configUrl=i+"/config",this.autoTrackProperties={},PlatformAPI.initConfig(e),this._queue=[],this.observers=[],this.updateConfig(this.configUrl,this.appId),e.isChildInstance?this._state={}:(logger$1.enabled=e.enableLog,this.instances=[],this._state={getSystemInfo:!1,initComplete:!1},PlatformAPI.setGlobal(this,this.name)),systemInformation.initDisableList(this.config.disablePresetProperties),this.store=new ThinkingDataPersistence(e,function(){t.config.asyncPersistence&&_.isFunction(t.config.persistenceComplete)&&t.config.persistenceComplete(t),t._updateState()}),this.enabled=!_.isBoolean(this.store._get("ta_enabled"))||this.store._get("ta_enabled"),this.isOptOut=!!_.isBoolean(this.store._get("ta_isOptOut"))&&this.store._get("ta_isOptOut"),!e.isChildInstance&&e.autoTrack&&(this.autoTrack=PlatformAPI.initAutoTrackInstance(this,e)),void 0!==this.config.enableBatch&&!1!==this.config.enableBatch&&(this.batchConsumer=new BatchConsumer(this.config,this),this.batchConsumer.batchInterval())}},{key:"initSystemInfo",value:function(){var e=this;this.config.isChildInstance||systemInformation.getSystemInfo(function(){e._updateState({getSystemInfo:!0})})}},{key:"updateConfig",value:function(e,t){var i=this,n=_.createExtraHeaders();n["content-type"]="application/json";var a=PlatformAPI.request({url:e+"?appid="+t,method:"GET",header:n,success:function(e){_.isUndefined(e)||_.isUndefined(e.data)||(logger$1.info("Get remote config success("+t+") :"+JSON.stringify(e.data)),_.isUndefined(e.data.data)||(i.config.syncBatchSize=e.data.data.sync_batch_size,i.config.syncInterval=e.data.data.sync_interval,i.config.disableEventList=e.data.data.disable_event_list,_.isUndefined(e.data.data.secret_key)||(e=e.data.data.secret_key,i.config.secretKey={publicKey:e.key,version:e.version})))},fail:function(e){logger$1.info("Get remote config fail("+t+") :"+e.errMsg)}});setTimeout(function(){(_.isObject(a)||_.isPromise(a))&&_.isFunction(a.abort)&&a.abort()},3e3)}},{key:"initInstance",value:function(e,t){if(!PlatformAPI.isWxPlat()||!this.isTADisable)if(this.config.isChildInstance)logger$1.warn("initInstance() cannot be called on child instance");else{if(_.isString(e)&&e!==this.name&&_.isUndefined(this[e])){t=new i(_.extend({},this.config,{enablePersistence:!1,isChildInstance:!0,name:e},t));return this[e]=t,this.instances.push(e),this[e]._state=this._state,t}logger$1.warn("initInstance() failed due to the name is invalid: "+e)}}},{key:"lightInstance",value:function(e){if(!PlatformAPI.isWxPlat()||!this.isTADisable)return this[e]}},{key:"_setAutoTrackProperties",value:function(e){_.extend(this.autoTrackProperties,e)}},{key:"init",value:function(){if(!PlatformAPI.isWxPlat()||!this.isTADisable){if(this.initSystemInfo(),this._state.initComplete)return!1;this._updateState({initComplete:!0}),logger$1.info("TDAnalytics SDK initialize success, AppId = "+this.config.appId+", ServerUrl = "+this.config.serverUrl+", Mode = "+this.config.model+", DeviceId = "+this.getDeviceId()+", Lib = "+Config.LIB_NAME+", LibVersion = "+Config.LIB_VERSION)}}},{key:"_isReady",value:function(){return this._state.getSystemInfo&&this._state.initComplete&&this.store.initComplete}},{key:"_updateState",value:function(e){var t=this;_.isObject(e)&&_.extend(this._state,e),this._onStateChange(),_.each(this.instances,function(e){t[e]._onStateChange()})}},{key:"_onStateChange",value:function(){var t=this;this._isReady()&&this._queue&&0<this._queue.length&&(_.each(this._queue,function(e){t[e[0]].apply(t,slice.call(e[1]))}),this._queue=[])}},{key:"_hasDisabled",value:function(){var e=!this.enabled||this.isOptOut;return e&&logger$1.info("SDK is Pause or Stop!"),e}},{key:"_sendRequest",value:function(e,t,i){if(!this._hasDisabled())if(_.isUndefined(this.config.disableEventList)||!this.config.disableEventList.includes(e.eventName)){t=_.isDate(t)?t:_.getCurrentDate();var n={data:[{"#type":e.type,"#time":_.formatDate(_.formatTimeZone(t,this.config.zoneOffset)),"#distinct_id":this.store.getDistinctId()}]};if(this.store.getAccountId()&&(n.data[0]["#account_id"]=this.store.getAccountId()),"track"===e.type||"track_update"===e.type||"track_overwrite"===e.type?(n.data[0]["#event_name"]=e.eventName,"track_update"===e.type||"track_overwrite"===e.type?n.data[0]["#event_id"]=e.extraId:e.firstCheckId&&(n.data[0]["#first_check_id"]=e.firstCheckId),n.data[0].properties=_.extend({"#zone_offset":_.getTimeZone(t,this.config.zoneOffset)},systemInformation.properties,this.autoTrackProperties,this.store.getSuperProperties(),this.dynamicProperties?this.dynamicProperties():{}),t=this.store.removeEventTimer(e.eventName),_.isUndefined(t)||(a=_.getCurrentTimeStamp()-t,86400<(a=parseFloat((a/1e3).toFixed(3)))?a=86400:a<0&&(a=0),n.data[0].properties["#duration"]=a)):n.data[0].properties={},_.isObject(e.properties)&&!_.isEmptyObject(e.properties)&&_.extend(n.data[0].properties,e.properties),_.searchObjDate(n.data[0],this.config.zoneOffset),1<this.config.maxRetries&&(n.data[0]["#uuid"]=_.UUIDv4()),n["#app_id"]=this.appId,this.notifyAllObserver("onDataEnqueue",{appId:this.appId,event:n.data[0]}),logger$1.info("Enqueue data, "+JSON.stringify(n,null,4)),"debug"===e.debugMode||"debugOnly"===e.debugMode)return senderQueue.runTimeout(this.config.sendTimeout)&&senderQueue.resetTimeout(),void senderQueue.enqueue(n,this.serverDebugUrl,{maxRetries:this.config.maxRetries,sendTimeout:this.config.sendTimeout,callback:e.onComplete,debugMode:e.debugMode,deviceId:this.getDeviceId()});var a="debug"===this.config.debugMode||"debugOnly"===this.config.debugMode?this.serverDebugUrl:this.serverUrl;if(_.isBoolean(this.config.enableEncrypt)&&!0===this.config.enableEncrypt&&(n.data[0]=_.generateEncryptyData(n.data[0],this.config.secretKey)),this.batchConsumer&&"none"===this.config.debugMode&&!i)return this.batchConsumer.add(n.data[0]),void(_.isFunction(e.onComplete)&&e.onComplete({code:0,msg:"success"}));i?(i=new FormData,"debug"===this.config.debugMode||"debugOnly"===this.config.debugMode?(i.append("source","client"),i.append("appid",this.appId),i.append("dryRun","debugOnly"===this.config.debugMode?1:0),i.append("deviceId",this.getDeviceId()),i.append("data",JSON.stringify(n.data[0])),navigator.sendBeacon(a,i)):(i=_.getCurrentTimeStamp(),n["#flush_time"]=i,navigator.sendBeacon(a,JSON.stringify(n))),_.isFunction(e.onComplete)&&e.onComplete({statusCode:200})):(senderQueue.runTimeout(this.config.sendTimeout)&&senderQueue.resetTimeout(),senderQueue.enqueue(n,a,{maxRetries:this.config.maxRetries,sendTimeout:this.config.sendTimeout,callback:e.onComplete,debugMode:this.config.debugMode,deviceId:this.getDeviceId()}))}else logger$1.info("Disabled Event : "+e.eventName)}},{key:"_isObjectParams",value:function(e){return _.isObject(e)&&_.isFunction(e.onComplete)}},{key:"track",value:function(e,t,i,n){var a;PlatformAPI.isWxPlat()&&(this.wxSdk&&(t=t||{},this.wxSdk.track(e,t)),this.isTADisable)||this._hasDisabled()||(this._isObjectParams(e)&&(e=(a=e).eventName,t=a.properties,i=a.time,n=a.onComplete),PropertyChecker.event(e)&&PropertyChecker.properties(t)||!this.config.strict?this._internalTrack(e,t,i,n,!1,!0):_.isFunction(n)&&n({code:-1,msg:"invalid parameters"}))}},{key:"trackInternal",value:function(e){this._hasDisabled()||this._internalTrack(e.eventName,e.properties,e.time,e.onComplete,!1,!0,e.debugMode)}},{key:"trackUpdate",value:function(e){var t,i;PlatformAPI.isWxPlat()&&this.isTADisable||this._hasDisabled()||(e&&e.eventId&&(PropertyChecker.event(e.eventName)&&PropertyChecker.properties(e.properties)||!this.config.strict)?this._isReady()?(t=_.checkCalibration(e.properties,e.time,this.config.enableCalibrationTime),i=_.isDate(e.time)?e.time:_.getCurrentDate(),this._sendRequest({type:"track_update",eventName:e.eventName,properties:t,onComplete:e.onComplete,extraId:e.eventId},i)):this._queue.push(["trackUpdate",[e]]):(logger$1.warn("Invalide parameter for trackUpdate: you should pass an object contains eventId to trackUpdate()"),_.isFunction(e.onComplete)&&e.onComplete({code:-1,msg:"invalid parameters"})))}},{key:"trackOverwrite",value:function(e){var t,i;PlatformAPI.isWxPlat()&&this.isTADisable||this._hasDisabled()||(e&&e.eventId&&(PropertyChecker.event(e.eventName)&&PropertyChecker.properties(e.properties)||!this.config.strict)?this._isReady()?(t=_.checkCalibration(e.properties,e.time,this.config.enableCalibrationTime),i=_.isDate(e.time)?e.time:_.getCurrentDate(),this._sendRequest({type:"track_overwrite",eventName:e.eventName,properties:t,onComplete:e.onComplete,extraId:e.eventId},i)):this._queue.push(["trackOverwrite",[e]]):(logger$1.warn("Invalide parameter for trackOverwrite: you should pass an object contains eventId to trackOverwrite()"),_.isFunction(e.onComplete)&&e.onComplete({code:-1,msg:"invalid parameters"})))}},{key:"trackFirstEvent",value:function(e){var t,i;PlatformAPI.isWxPlat()&&this.isTADisable||this._hasDisabled()||(e&&e.eventName&&(PropertyChecker.event(e.eventName)&&PropertyChecker.properties(e.properties)||!this.config.strict)?this._isReady()?(t=_.checkCalibration(e.properties,e.time,this.config.enableCalibrationTime),i=_.isDate(e.time)?e.time:_.getCurrentDate(),this._sendRequest({type:"track",eventName:e.eventName,properties:t,onComplete:e.onComplete,firstCheckId:e.firstCheckId||this.getDeviceId()},i)):this._queue.push(["trackFirstEvent",[e]]):(logger$1.warn("Invalide parameter for trackFirstEvent: you should pass an object contains eventName to trackFirstEvent()"),_.isFunction(e.onComplete)&&e.onComplete({code:-1,msg:"invalid parameters"})))}},{key:"_internalTrack",value:function(e,t,i,n,a,r,s){!r&&(this.wxSdk&&((t=t||{}).trackBy="ThinkingData",this.wxSdk.track(e,t)),this.isTADisable)||this._hasDisabled()||(r=_.checkCalibration(t,i,this.config.enableCalibrationTime),i=_.isDate(i)?i:_.getCurrentDate(),this._isReady()?this._sendRequest({type:"track",eventName:e,debugMode:s,properties:r,onComplete:n},i,a):this._queue.push(["_internalTrack",[e,t,i,n,a,!0]]))}},{key:"userSet",value:function(e,t,i){var n;PlatformAPI.isWxPlat()&&this.isTADisable||this._hasDisabled()||(this._isObjectParams(e)&&(e=(n=e).properties,t=n.time,i=n.onComplete),PropertyChecker.propertiesMust(e)||!this.config.strict?(t=_.isDate(t)?t:_.getCurrentDate(),this._isReady()?this._sendRequest({type:"user_set",properties:e,onComplete:i},t):this._queue.push(["userSet",[e,t,i]])):(logger$1.warn("calling userSet failed due to invalid arguments"),_.isFunction(i)&&i({code:-1,msg:"invalid parameters"})))}},{key:"userSetOnce",value:function(e,t,i){var n;PlatformAPI.isWxPlat()&&this.isTADisable||this._hasDisabled()||(this._isObjectParams(e)&&(e=(n=e).properties,t=n.time,i=n.onComplete),PropertyChecker.propertiesMust(e)||!this.config.strict?(t=_.isDate(t)?t:_.getCurrentDate(),this._isReady()?this._sendRequest({type:"user_setOnce",properties:e,onComplete:i},t):this._queue.push(["userSetOnce",[e,t,i]])):(logger$1.warn("calling userSetOnce failed due to invalid arguments"),_.isFunction(i)&&i({code:-1,msg:"invalid parameters"})))}},{key:"userUnset",value:function(e,t,i){var n;PlatformAPI.isWxPlat()&&this.isTADisable||this._hasDisabled()||(this._isObjectParams(n)&&(e=n.property,t=n.time,i=n.onComplete),PropertyChecker.propertyName(e)||!this.config.strict?(t=_.isDate(t)?t:_.getCurrentDate(),this._isReady()?((n={})[e]=0,this._sendRequest({type:"user_unset",properties:n,onComplete:i},t)):this._queue.push(["userUnset",[e,i,t]])):(logger$1.warn("calling userUnset failed due to invalid arguments"),_.isFunction(i)&&i({code:-1,msg:"invalid parameters"})))}},{key:"userDel",value:function(e,t){var i;PlatformAPI.isWxPlat()&&this.isTADisable||this._hasDisabled()||(this._isObjectParams(e)&&(e=(i=e).time,t=i.onComplete),e=_.isDate(e)?e:_.getCurrentDate(),this._isReady()?this._sendRequest({type:"user_del",onComplete:t},e):this._queue.push(["userDel",[e,t]]))}},{key:"userAdd",value:function(e,t,i){var n;PlatformAPI.isWxPlat()&&this.isTADisable||this._hasDisabled()||(this._isObjectParams(e)&&(e=(n=e).properties,t=n.time,i=n.onComplete),PropertyChecker.userAddProperties(e)||!this.config.strict?(t=_.isDate(t)?t:_.getCurrentDate(),this._isReady()?this._sendRequest({type:"user_add",properties:e,onComplete:i},t):this._queue.push(["userAdd",[e,t,i]])):(logger$1.warn("calling userAdd failed due to invalid arguments"),_.isFunction(i)&&i({code:-1,msg:"invalid parameters"})))}},{key:"userAppend",value:function(e,t,i){var n;PlatformAPI.isWxPlat()&&this.isTADisable||this._hasDisabled()||(this._isObjectParams(e)&&(e=(n=e).properties,t=n.time,i=n.onComplete),PropertyChecker.userAppendProperties(e)||!this.config.strict?(t=_.isDate(t)?t:_.getCurrentDate(),this._isReady()?this._sendRequest({type:"user_append",properties:e,onComplete:i},t):this._queue.push(["userAppend",[e,t,i]])):(logger$1.warn("calling userAppend failed due to invalid arguments"),_.isFunction(i)&&i({code:-1,msg:"invalid parameters"})))}},{key:"userUniqAppend",value:function(e,t,i){var n;PlatformAPI.isWxPlat()&&this.isTADisable||this._hasDisabled()||(this._isObjectParams(e)&&(e=(n=e).properties,t=n.time,i=n.onComplete),PropertyChecker.userAppendProperties(e)||!this.config.strict?(t=_.isDate(t)?t:_.getCurrentDate(),this._isReady()?this._sendRequest({type:"user_uniq_append",properties:e,onComplete:i},t):this._queue.push(["userUniqAppend",[e,t,i]])):(logger$1.warn("calling userAppend failed due to invalid arguments"),_.isFunction(i)&&i({code:-1,msg:"invalid parameters"})))}},{key:"flush",value:function(){PlatformAPI.isWxPlat()&&this.isTADisable||this.batchConsumer&&"none"===this.config.debugMode&&this.batchConsumer.flush()}},{key:"authorizeOpenID",value:function(e){this.identify(e)}},{key:"identify",value:function(e){if(!(PlatformAPI.isWxPlat()&&this.isTADisable||this._hasDisabled()||void 0===e||""===e.trim())){if("number"==typeof e)e=String(e);else if("string"!=typeof e)return!1;this.store.setDistinctId(e),logger$1.info("Setting distinct ID, DistinctId = "+e),this.notifyAllObserver("onAccountChanged",{accountId:this.getAccountId(),distinctId:e})}}},{key:"getDistinctId",value:function(){return PlatformAPI.isWxPlat()&&this.isTADisable?"":this.store.getDistinctId()}},{key:"login",value:function(e){if(!(PlatformAPI.isWxPlat()&&this.isTADisable||this._hasDisabled()||void 0===e||""===e.trim())){if("number"==typeof e)e=String(e);else if("string"!=typeof e)return!1;this.store.setAccountId(e),logger$1.info("Login SDK, AccountId = "+e),this.notifyAllObserver("onAccountChanged",{accountId:e,distinctId:this.getDistinctId()})}}},{key:"getAccountId",value:function(){return PlatformAPI.isWxPlat()&&this.isTADisable?"":this.store.getAccountId()}},{key:"logout",value:function(){PlatformAPI.isWxPlat()&&this.isTADisable||this._hasDisabled()||(this.store.setAccountId(null),logger$1.info("Logout SDK"),this.notifyAllObserver("onAccountChanged",{accountId:"",distinctId:this.getDistinctId()}))}},{key:"notifyAllObserver",value:function(e,t){for(var i=0;i<this.observers.length;i++)this.observers[i](e,t)}},{key:"setSuperProperties",value:function(e){PlatformAPI.isWxPlat()&&this.isTADisable||this._hasDisabled()||(PropertyChecker.propertiesMust(e)||!this.config.strict?this.store.setSuperProperties(e):logger$1.warn("setSuperProperties parameter must be a valid property value"))}},{key:"clearSuperProperties",value:function(){PlatformAPI.isWxPlat()&&this.isTADisable||this._hasDisabled()||this.store.setSuperProperties({},!0)}},{key:"unsetSuperProperty",value:function(e){var t;PlatformAPI.isWxPlat()&&this.isTADisable||this._hasDisabled()||_.isString(e)&&(delete(t=this.getSuperProperties())[e],this.store.setSuperProperties(t,!0))}},{key:"getSuperProperties",value:function(){return PlatformAPI.isWxPlat()&&this.isTADisable?{}:this.store.getSuperProperties()}},{key:"getPresetProperties",value:function(){if(PlatformAPI.isWxPlat()&&this.isTADisable)return{};var e,t,i,n=systemInformation.properties,a={};this.config.disablePresetProperties.includes("#os")||(e=n["#os"],a.os=_.isUndefined(e)?"":e),this.config.disablePresetProperties.includes("#screen_width")||(e=n["#screen_width"],a.screenWidth=_.isUndefined(e)?0:e),this.config.disablePresetProperties.includes("#screen_height")||(t=n["#screen_height"],a.screenHeight=_.isUndefined(t)?0:t),this.config.disablePresetProperties.includes("#network_type")||(t=n["#network_type"],a.networkType=_.isUndefined(t)?"":t),this.config.disablePresetProperties.includes("#device_model")||(i=n["#device_model"],a.deviceModel=_.isUndefined(i)?"":i),this.config.disablePresetProperties.includes("#os_version")||(i=n["#os_version"],a.osVersion=_.isUndefined(i)?"":i),this.config.disablePresetProperties.includes("#device_id")||(a.deviceId=this.getDeviceId());var r=_.getTimeZone(_.getCurrentDate(),this.config.zoneOffset);return a.zoneOffset=r,this.config.disablePresetProperties.includes("#manufacturer")||(n=n["#manufacturer"],a.manufacturer=_.isUndefined(n)?"":n),a.toEventPresetProperties=function(){var e={};return a.deviceModel&&(e["#device_model"]=a.deviceModel),a.deviceId&&(e["#device_id"]=a.deviceId),a.screenWidth&&(e["#screen_width"]=a.screenWidth),a.screenHeight&&(e["#screen_height"]=a.screenHeight),a.os&&(e["#os"]=a.os),a.osVersion&&(e["#os_version"]=a.osVersion),a.networkType&&(e["#network_type"]=a.networkType),e["#zone_offset"]=r,a.manufacturer&&(e["#manufacturer"]=a.manufacturer),e},a}},{key:"setDynamicSuperProperties",value:function(e){if(PlatformAPI.isWxPlat()&&this.isTADisable)return{};this._hasDisabled()||("function"==typeof e?PropertyChecker.properties(e())||!this.config.strict?this.dynamicProperties=e:logger$1.warn("A dynamic public property must return a valid property value"):logger$1.warn("setDynamicSuperProperties parameter must be a function type"))}},{key:"registerAnalyticsObserver",value:function(e){this._hasDisabled()||("function"==typeof e?this.observers.push(e):logger$1.warn("registerAnalyticsObserver parameter must be a function type"))}},{key:"timeEvent",value:function(e,t){if(PlatformAPI.isWxPlat()&&this.isTADisable)return{};this._hasDisabled()||(t=_.isDate(t)?t:_.getCurrentDate(),this._isReady()?PropertyChecker.event(e)||!this.config.strict?this.store.setEventTimer(e,t.getTime()):logger$1.warn("calling timeEvent failed due to invalid eventName: "+e):this._queue.push(["timeEvent",[e,t]]))}},{key:"getDeviceId",value:function(){return PlatformAPI.isWxPlat()&&this.isTADisable?"":systemInformation.properties["#device_id"]}},{key:"enableTracking",value:function(e){PlatformAPI.isWxPlat()&&this.isTADisable||(this.enabled=e,this.store._set("ta_enabled",e))}},{key:"optOutTracking",value:function(){PlatformAPI.isWxPlat()&&this.isTADisable||(this.store.setSuperProperties({},!0),this.store.setDistinctId(_.UUID()),this.store.setAccountId(null),this._queue.splice(0,this._queue.length),this.isOptOut=!0,this.store._set("ta_isOptOut",!0))}},{key:"optOutTrackingAndDeleteUser",value:function(){var e;PlatformAPI.isWxPlat()&&this.isTADisable||(e=_.getCurrentDate(),this._sendRequest({type:"user_del"},e),this.optOutTracking())}},{key:"optInTracking",value:function(){PlatformAPI.isWxPlat()&&this.isTADisable||(this.isOptOut=!1,this.store._set("ta_isOptOut",!1))}},{key:"setTrackStatus",value:function(e){if(!PlatformAPI.isWxPlat()||!this.isTADisable){switch(e){case"PAUSE":this.eventSaveOnly=!1,this.optInTracking(),this.enableTracking(!1);break;case"STOP":this.eventSaveOnly=!1,this.optOutTracking(!0);break;case"SAVE_ONLY":break;case"NORMAL":default:this.eventSaveOnly=!1,this.optInTracking(),this.enableTracking(!0)}logger$1.info("Change Status to "+e)}}}]),i}(),DEFAULT_CONFIG$1={name:"thinkingdata",enableLog:!0,enableNative:!1},ThinkingDataAPIForNative=function(){function i(e){_classCallCheck(this,i),e.appId=e.appId?_.checkAppId(e.appId):_.checkAppId(e.appid),e.serverUrl=e.serverUrl?_.checkUrl(e.serverUrl):_.checkUrl(e.server_url);var t=_.extend({},DEFAULT_CONFIG$1,PlatformAPI.getConfig());_.isObject(e)?this.config=_.extend(t,e):this.config=t,this._init(this.config)}return _createClass(i,[{key:"_isNativePlatform",value:function(){return!(!(this._isIOS()||this._isAndroid()||this._isOpenHarmony())||!this.config.enableNative)}},{key:"_isIOS",value:function(){return!(!cc.sys.isNative||"iOS"!==cc.sys.os)}},{key:"_isAndroid",value:function(){return!(!cc.sys.isNative||"Android"!==cc.sys.os)}},{key:"_isOpenHarmony",value:function(){return!(!cc.sys.isNative||"OpenHarmony"!==cc.sys.os)}},{key:"_init",value:function(e){this.name=e.name,this.appId=e.appId||e.appid,this._isNativePlatform()?(this.initInstanceForNative(e),this._readStorage(e)):this.taJs=new ThinkingAnalyticsAPIForJS(e)}},{key:"_readStorage",value:function(e){var t=this,i=e.persistenceName,n=e.persistenceNameOld;e.isChildInstance&&(i=e.persistenceName+"_"+e.name,n=e.persistenceNameOld+"_"+e.name),this._state=PlatformAPI.getStorage(i)||{},_.isEmptyObject(this._state)&&(this._state=PlatformAPI.getStorage(n)||{}),_.isEmptyObject(this._state)?PlatformAPI.getStorage(i,!0,function(e){_.isEmptyObject(e)?PlatformAPI.getStorage(n,!0,function(e){t._state=_.extend2Layers({},e,t._state)}):t._state=_.extend2Layers({},e,t._state),t._state.distinct_id&&t.identifyForNative(t._state.distinct_id),t._state.account_id&&t.loginForNative(t._state.account_id)}):(this._state.distinct_id&&this.identifyForNative(this._state.distinct_id),this._state.account_id&&this.loginForNative(this._state.account_id))}},{key:"initInstance",value:function(e,t){return this._isNativePlatform()?_.isUndefined(t)?this[e]=new ThinkingAnalyticsAPI(this.config):this[e]=new ThinkingAnalyticsAPI(t):this[e]=this.taJs.initInstance(e,t),this[e]}},{key:"lightInstance",value:function(){return this._isNativePlatform()?this.lightInstanceForNative(this.appId):this}},{key:"init",value:function(){if(this._isNativePlatform()){var e=window,t=this;return e.__autoTrackCallback=function(e){if(_.isFunction(t.config.autoTrack.callback)){e=t.config.autoTrack.callback(e);return JSON.stringify(e)}return"{}"},void this.startThinkingAnalyticsForNative(this.appId)}this.taJs.init()}},{key:"track",value:function(e,t,i,n){this._isNativePlatform()?this.trackForNative(e,t,i,this.appId):this.taJs.track(e,t,i,n)}},{key:"trackUpdate",value:function(e){this._isNativePlatform()?this.trackUpdateForNative(e,this.appId):this.taJs.trackUpdate(e)}},{key:"trackOverwrite",value:function(e){this._isNativePlatform()?this.trackOverwriteForNative(e,this.appId):this.taJs.trackOverwrite(e)}},{key:"trackFirstEvent",value:function(e){this._isNativePlatform()?this.trackFirstEventForNative(e,this.appId):this.taJs.trackFirstEvent(e)}},{key:"userSet",value:function(e,t,i){this._isNativePlatform()?this.userSetForNative(e,this.appId):this.taJs.userSet(e,t,i)}},{key:"userSetOnce",value:function(e,t,i){this._isNativePlatform()?this.userSetOnceForNative(e,this.appId):this.taJs.userSetOnce(e,t,i)}},{key:"userUnset",value:function(e,t,i){this._isNativePlatform()?this.userUnsetForNative(e,this.appId):this.taJs.userUnset(e,t,i)}},{key:"userDel",value:function(e,t){this._isNativePlatform()?this.userDelForNative(this.appId):this.taJs.userDel(e,t)}},{key:"userAdd",value:function(e,t,i){this._isNativePlatform()?this.userAddForNative(e,this.appId):this.taJs.userAdd(e,t,i)}},{key:"userAppend",value:function(e,t,i){this._isNativePlatform()?this.userAppendForNative(e,this.appId):this.taJs.userAppend(e,t,i)}},{key:"userUniqAppend",value:function(e,t,i){this._isNativePlatform()?this.userUniqAppendForNative(e,this.appId):this.taJs.userUniqAppend(e,t,i)}},{key:"flush",value:function(){this._isNativePlatform()?this.flushForNative(this.appId):this.taJs.flush()}},{key:"authorizeOpenID",value:function(e){this.identify(e)}},{key:"identify",value:function(e){this._isNativePlatform()?this.identifyForNative(e,this.appId):this.taJs.identify(e)}},{key:"getDistinctId",value:function(){return this._isNativePlatform()?this.getDistinctIdForNative(this.appId):this.taJs.getDistinctId()}},{key:"login",value:function(e){this._isNativePlatform()?this.loginForNative(e,this.appId):this.taJs.login(e)}},{key:"getAccountId",value:function(){return this._isNativePlatform()?this.getAccountIdForNative(this.appId):this.taJs.getAccountId()}},{key:"logout",value:function(){this._isNativePlatform()?this.logoutForNative(this.appId):this.taJs.logout()}},{key:"setSuperProperties",value:function(e){this._isNativePlatform()?this.setSuperPropertiesForNative(e,this.appId):this.taJs.setSuperProperties(e)}},{key:"clearSuperProperties",value:function(){this._isNativePlatform()?this.clearSuperPropertiesForNative(this.appId):this.taJs.clearSuperProperties()}},{key:"unsetSuperProperty",value:function(e){this._isNativePlatform()?this.unsetSuperPropertyForNative(e,this.appId):this.taJs.unsetSuperProperty(e)}},{key:"getSuperProperties",value:function(){return this._isNativePlatform()?this.getSuperPropertiesForNative(this.appId):this.taJs.getSuperProperties()}},{key:"getPresetProperties",value:function(){if(this._isNativePlatform()){var e=this.getPresetPropertiesForNative(this.appId),t={},i=e["#os"];t.os=_.isUndefined(i)?"":i;i=e["#screen_width"];t.screenWidth=_.isUndefined(i)?0:i;i=e["#screen_height"];t.screenHeight=_.isUndefined(i)?0:i;i=e["#network_type"];t.networkType=_.isUndefined(i)?"":i;i=e["#device_model"];t.deviceModel=_.isUndefined(i)?"":i;i=e["#os_version"];t.osVersion=_.isUndefined(i)?"":i,t.deviceId=this.getDeviceId();var n=0-(new Date).getTimezoneOffset()/60;t.zoneOffset=n;e=e["#manufacturer"];return t.manufacturer=_.isUndefined(e)?"":e,t.toEventPresetProperties=function(){return{"#device_model":t.deviceModel,"#device_id":t.deviceId,"#screen_width":t.screenWidth,"#screen_height":t.screenHeight,"#os":t.os,"#os_version":t.osVersion,"#network_type":t.networkType,"#zone_offset":n,"#manufacturer":t.manufacturer}},t}return this.taJs.getPresetProperties()}},{key:"setDynamicSuperProperties",value:function(t){this._isNativePlatform()?"function"==typeof t?(this.dynamicProperties=t,window.__dynamicPropertiesForNative=function(e){console.log("__dynamicPropertiesForNative: native msg: ",e);e=t(),e=_.encodeDates(e);return JSON.stringify(e)},this.setDynamicSuperPropertiesForNative("__dynamicPropertiesForNative")):logger.warn("setDynamicSuperProperties parameter must be a function type"):this.taJs.setDynamicSuperProperties(t)}},{key:"timeEvent",value:function(e,t){return this._isNativePlatform()?this.timeEventForNative(e,this.appId):this.taJs.timeEvent(e,t)}},{key:"getDeviceId",value:function(){return this._isNativePlatform()?this.getDeviceIdForNative(this.appId):this.taJs.getDeviceId()}},{key:"enableTracking",value:function(e){this._isNativePlatform()?this.enableTrackingForNative(e,this.appId):this.taJs.enableTracking(e)}},{key:"optOutTracking",value:function(){this._isNativePlatform()?this.optOutTrackingForNative(this.appId):this.taJs.optOutTracking()}},{key:"optOutTrackingAndDeleteUser",value:function(){this._isNativePlatform()?this.optOutTrackingAndDeleteUserForNative(this.appId):this.taJs.optOutTrackingAndDeleteUser()}},{key:"optInTracking",value:function(){this._isNativePlatform()?this.optInTrackingForNative(this.appId):this.taJs.optInTracking()}},{key:"setTrackStatus",value:function(e){this._isNativePlatform()?this.setTrackStatusForNative(e,this.appId):this.taJs.setTrackStatus(e)}},{key:"trackForNative",value:function(e,t,i,n){i=_.isDate(i)?_.formatDate(i):"";_.isUndefined(t)&&(t={}),t=_.extend(t,this.dynamicProperties?this.dynamicProperties():{}),t=_.encodeDates(t),this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","track","(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V",e,JSON.stringify(t),i,n):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","track:properties:time:appId:",e,JSON.stringify(t),i,n):this._isOpenHarmony()&&jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","track",JSON.stringify({eventName:e,properties:t,appId:n}),!0)}},{key:"trackUpdateForNative",value:function(e,t){e.properties=_.extend(_.isUndefined(e.properties)?{}:e.properties,this.dynamicProperties?this.dynamicProperties():{}),e=_.encodeDates(e),this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","trackUpdate","(Ljava/lang/String;Ljava/lang/String;)V",JSON.stringify(e),t):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","trackUpdate:appId:",JSON.stringify(e),t):this._isOpenHarmony()&&jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","trackUpdate",JSON.stringify({event:e,appId:t}),!0)}},{key:"trackFirstEventForNative",value:function(e,t){e.properties=_.extend(_.isUndefined(e.properties)?{}:e.properties,this.dynamicProperties?this.dynamicProperties():{}),e=_.encodeDates(e),this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","trackFirstEvent","(Ljava/lang/String;Ljava/lang/String;)V",JSON.stringify(e),t):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","trackFirstEvent:appId:",JSON.stringify(e),t):this._isOpenHarmony()&&jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","trackFirst",JSON.stringify({event:e,appId:t}),!0)}},{key:"trackOverwriteForNative",value:function(e,t){e.properties=_.extend(_.isUndefined(e.properties)?{}:e.properties,this.dynamicProperties?this.dynamicProperties():{}),e=_.encodeDates(e),this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","trackOverwrite","(Ljava/lang/String;Ljava/lang/String;)V",JSON.stringify(e),t):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","trackOverwrite:appId:",JSON.stringify(e),t):this._isOpenHarmony()&&jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","trackOverwrite",JSON.stringify({event:e,appId:t}),!0)}},{key:"timeEventForNative",value:function(e,t){this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","timeEvent","(Ljava/lang/String;Ljava/lang/String;)V",e,t):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","timeEvent:appId:",e,t):this._isOpenHarmony()&&jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","timeEvent",JSON.stringify({eventName:e,appId:t}),!0)}},{key:"loginForNative",value:function(e,t){this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","login","(Ljava/lang/String;Ljava/lang/String;)V",e,t):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","login:appId:",e,t):this._isOpenHarmony()&&jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","login",JSON.stringify({accountId:e,appId:t}),!0)}},{key:"logoutForNative",value:function(e){this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","logout","(Ljava/lang/String;)V",e):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","logout:",e):this._isOpenHarmony()&&jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","logout",e,!0)}},{key:"setSuperPropertiesForNative",value:function(e,t){e=_.encodeDates(e),this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","setSuperProperties","(Ljava/lang/String;Ljava/lang/String;)V",JSON.stringify(e),t):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","setSuperProperties:appId:",JSON.stringify(e),t):this._isOpenHarmony()&&jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","setSuperProperties",JSON.stringify({properties:e,appId:t}),!0)}},{key:"getSuperPropertiesForNative",value:function(e){var t="{}";return this._isAndroid()?t=jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","getSuperProperties","(Ljava/lang/String;)Ljava/lang/String;",e):this._isIOS()?t=jsb.reflection.callStaticMethod("CocosCreatorProxyApi","getSuperProperties:",e):this._isOpenHarmony()&&(t=jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","getSuperProperties",e,!0)),JSON.parse(t)}},{key:"unsetSuperPropertyForNative",value:function(e,t){this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","unsetSuperProperty","(Ljava/lang/String;Ljava/lang/String;)V",e,t):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","unsetSuperProperty:appId:",e,t):this._isOpenHarmony()&&jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","unsetSuperProperty",JSON.stringify({property:e,appId:t}),!0)}},{key:"clearSuperPropertiesForNative",value:function(e){this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","clearSuperProperties","(Ljava/lang/String;)V",e):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","clearSuperProperties:",e):this._isOpenHarmony()&&jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","clearSuperProperties",e,!0)}},{key:"userSetForNative",value:function(e,t){e=_.encodeDates(e),this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","userSet","(Ljava/lang/String;Ljava/lang/String;)V",JSON.stringify(e),t):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","userSet:appId:",JSON.stringify(e),t):this._isOpenHarmony()&&jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","userSet",JSON.stringify({properties:e,appId:t}),!0)}},{key:"userSetOnceForNative",value:function(e,t){e=_.encodeDates(e),this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","userSetOnce","(Ljava/lang/String;Ljava/lang/String;)V",JSON.stringify(e),t):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","userSetOnce:appId:",JSON.stringify(e),t):this._isOpenHarmony()&&jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","userSetOnce",JSON.stringify({properties:e,appId:t}),!0)}},{key:"userAppendForNative",value:function(e,t){e=_.encodeDates(e),this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","userAppend","(Ljava/lang/String;Ljava/lang/String;)V",JSON.stringify(e),t):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","userAppend:appId:",JSON.stringify(e),t):this._isOpenHarmony()&&jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","userAppend",JSON.stringify({properties:e,appId:t}),!0)}},{key:"userUniqAppendForNative",value:function(e,t){e=_.encodeDates(e),this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","userUniqAppend","(Ljava/lang/String;Ljava/lang/String;)V",JSON.stringify(e),t):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","userUniqAppend:appId:",JSON.stringify(e),t):this._isOpenHarmony()&&jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","userUniqAppend",JSON.stringify({properties:e,appId:t}),!0)}},{key:"userAddForNative",value:function(e,t){e=_.encodeDates(e),this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","userAdd","(Ljava/lang/String;Ljava/lang/String;)V",JSON.stringify(e),t):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","userAdd:appId:",JSON.stringify(e),t):this._isOpenHarmony()&&jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","userAdd",JSON.stringify({properties:e,appId:t}),!0)}},{key:"userUnsetForNative",value:function(e,t){this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","userUnset","(Ljava/lang/String;Ljava/lang/String;)V",e,t):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","userUnset:appId:",e,t):this._isOpenHarmony()&&jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","userUnset",JSON.stringify({property:e,appId:t}),!0)}},{key:"userDelForNative",value:function(e){this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","userDel","(Ljava/lang/String;)V",e):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","userDel:",e):this._isOpenHarmony()&&jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","userDel",e,!0)}},{key:"flushForNative",value:function(e){this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","flush","(Ljava/lang/String;)V",e):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","flush:",e):this._isOpenHarmony()&&jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","flush",e,!0)}},{key:"authorizeOpenIDForNative",value:function(e,t){this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","authorizeOpenID","(Ljava/lang/String;Ljava/lang/String;)V",e,t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","authorizeOpenID:appId:",e,t)}},{key:"identifyForNative",value:function(e,t){this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","identify","(Ljava/lang/String;Ljava/lang/String;)V",e,t):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","identify:appId:",e,t):this._isOpenHarmony()&&jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","setDistinctId",JSON.stringify({distinctId:e,appId:t}),!0)}},{key:"initInstanceForNative",value:function(e){this._isAndroid()?(jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","setCustomerLibInfo","(Ljava/lang/String;Ljava/lang/String;)V",Config.LIB_NAME,Config.LIB_VERSION),jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","initWithConfig","(Ljava/lang/String;)V",JSON.stringify(e))):this._isIOS()?(jsb.reflection.callStaticMethod("CocosCreatorProxyApi","setCustomerLibInfoWithLibName:libVersion:",Config.LIB_NAME,Config.LIB_VERSION),jsb.reflection.callStaticMethod("CocosCreatorProxyApi","initWithConfig:",JSON.stringify(e))):this._isOpenHarmony()&&(jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","setCustomerLibInfo",JSON.stringify({lib:Config.LIB_NAME,version:Config.LIB_VERSION}),!0),jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","initWithConfig",JSON.stringify(e),!0))}},{key:"lightInstanceForNative",value:function(e){return this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","lightInstance","(Ljava/lang/String;)Ljava/lang/String;",e):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","lightInstance:",e):void 0}},{key:"startThinkingAnalyticsForNative",value:function(e){this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","startThinkingAnalytics","(Ljava/lang/String;)V",e):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","startThinkingAnalytics:",e)}},{key:"setDynamicSuperPropertiesForNative",value:function(e,t){this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","setDynamicSuperProperties","(Ljava/lang/String;Ljava/lang/String;)V",e,t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","setDynamicSuperProperties:appId:",e,t)}},{key:"getDeviceIdForNative",value:function(e){return this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","getDeviceId","(Ljava/lang/String;)Ljava/lang/String;",e):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","getDeviceId:",e):this._isOpenHarmony()?jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","getDeviceId",e,!0):void 0}},{key:"getDistinctIdForNative",value:function(e){return this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","getDistinctId","(Ljava/lang/String;)Ljava/lang/String;",e):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","getDistinctId:",e):this._isOpenHarmony()?jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","getDistinctId",e,!0):void 0}},{key:"getAccountIdForNative",value:function(e){return this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","getAccountId","(Ljava/lang/String;)Ljava/lang/String;",e):this._isIOS()?jsb.reflection.callStaticMethod("CocosCreatorProxyApi","getAccountId:",e):this._isOpenHarmony()?jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","getAccountId",e,!0):void 0}},{key:"getPresetPropertiesForNative",value:function(e){var t="{}";return this._isAndroid()?t=jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","getPresetProperties","(Ljava/lang/String;)Ljava/lang/String;",e):this._isIOS()?t=jsb.reflection.callStaticMethod("CocosCreatorProxyApi","getPresetProperties:",e):this._isOpenHarmony()&&(t=jsb.reflection.callStaticMethod("entry/src/main/ets/CocosCreatorProxyApi","getPresetProperties",e,!0)),JSON.parse(t)}},{key:"enableTrackingForNative",value:function(e,t){this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","enableTracking","(Ljava/lang/String;Ljava/lang/String;)V",e.toString(),t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","enableTracking:appId:",e.toString(),t)}},{key:"optOutTrackingForNative",value:function(e){this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","optOutTracking","(Ljava/lang/String;)V",e):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","optOutTracking:",e)}},{key:"optOutTrackingAndDeleteUserForNative",value:function(e){this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","optOutTrackingAndDeleteUser","(Ljava/lang/String;)V",e):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","optOutTrackingAndDeleteUser:",e)}},{key:"optInTrackingForNative",value:function(e){this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","optInTracking","(Ljava/lang/String;)V",e):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","optInTracking:",e)}},{key:"setTrackStatusForNative",value:function(e,t){this._isAndroid()?jsb.reflection.callStaticMethod("com/cocos/game/CocosCreatorProxyApi","setTrackStatus","(Ljava/lang/String;Ljava/lang/String;)V",e,t):this._isIOS()&&jsb.reflection.callStaticMethod("CocosCreatorProxyApi","setTrackStatus:appId:",e,t)}}]),i}();window.ThinkingAnalyticsAPI=ThinkingDataAPIForNative,window.ThinkingAnalyticsAPIForJS=ThinkingDataAPI;var TDAnalytics=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"_shareInstance",value:function(e){return void 0!==this._instanceMaps[e]?this._instanceMaps[e]:void 0!==this._defaultInstance?this._defaultInstance:void 0}},{key:"init",value:function(e){var t=new ThinkingDataAPIForNative(e);t.init(),void 0!==t&&(void 0===this._defaultInstance&&(this._defaultInstance=t,this._instanceMaps={}),this._instanceMaps[e.appId]=t)}},{key:"lightInstance",value:function(e){return this._shareInstance(e).lightInstance()}},{key:"track",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).track(e.eventName,e.properties,e.time,e.onComplete)}},{key:"trackFirst",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).trackFirstEvent(e)}},{key:"trackUpdate",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).trackUpdate(e)}},{key:"trackOverwrite",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).trackOverwrite(e)}},{key:"timeEvent",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).timeEvent(e.eventName,e.time)}},{key:"userSet",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).userSet(e.properties,e.time,e.onComplete)}},{key:"userSetOnce",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).userSetOnce(e.properties,e.time,e.onComplete)}},{key:"userUnset",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).userUnset(e.property,e.time,e.onComplete)}},{key:"userAdd",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).userAdd(e.properties,e.time,e.onComplete)}},{key:"userAppend",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).userAppend(e.properties,e.time,e.onComplete)}},{key:"userUniqAppend",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).userUniqAppend(e.properties,e.time,e.onComplete)}},{key:"userDelete",value:function(e,t){e=0<arguments.length&&void 0!==e?e:{},t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).userDel(e.time,e.onComplete)}},{key:"setSuperProperties",value:function(e,t){t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).setSuperProperties(e)}},{key:"unsetSuperProperty",value:function(e,t){t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).unsetSuperProperty(e)}},{key:"clearSuperProperties",value:function(e){this._shareInstance(e).clearSuperProperties()}},{key:"getSuperProperties",value:function(e){return this._shareInstance(e).getSuperProperties()}},{key:"setDynamicSuperProperties",value:function(e,t){t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).setDynamicSuperProperties(e)}},{key:"getPresetProperties",value:function(e){return this._shareInstance(e).getPresetProperties()}},{key:"login",value:function(e,t){t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).login(e)}},{key:"logout",value:function(e){this._shareInstance(e).logout()}},{key:"setDistinctId",value:function(e,t){t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).identify(e)}},{key:"getDistinctId",value:function(e){return this._shareInstance(e).getDistinctId()}},{key:"getAccountId",value:function(e){return this._shareInstance(e).getAccountId()}},{key:"getSDKVersion",value:function(){return"1.0.0"}},{key:"getDeviceId",value:function(e){return this._shareInstance(e).getDeviceId()}},{key:"flush",value:function(e){this._shareInstance(e).flush()}},{key:"setTrackStatus",value:function(e,t){t=1<arguments.length&&void 0!==t?t:"";this._shareInstance(t).setTrackStatus(e)}},{key:"ThinkingDataAPI",value:function(){return ThinkingDataAPIForNative}}]),e}();window.TDAnalytics=TDAnalytics,module.exports=TDAnalytics;