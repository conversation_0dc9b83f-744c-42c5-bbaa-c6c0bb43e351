import { BillingParams, LoginParams } from "../Modules/Platform/Typing";
export default class ZeusServer {
    static getFingerprint(params: Map<string, any>): string;
    static getBillingQrcodeImage(module_name: string): any;
    static getBillingEnv(module_name: string): any;
    private static getAppModel;
    static sendRequest(sender: any, params: any, sub_path: string, success: Function, fail: Function): void;
    static normalizeZeusServerResponse(resp: any): {
        code: number;
        msg: string;
        data?: undefined;
    } | {
        code: number;
        msg: string;
        data: any;
    };
    static verifyLoginStatus(sender: any, account: string, success: Function, fail: Function): void;
    static login(sender: any, params: LoginParams, success: Function, fail: Function): void;
    static billing(sender: any, sendName: string, params: BillingParams, success: Function, fail: Function): void;
    static sendSubscribeMessage(sender: any, sendName: string, openId: string, tmplId: string, success: Function, fail: Function): void;
    static gameInfo(sender: any, success: Function, fail: Function): void;
}
