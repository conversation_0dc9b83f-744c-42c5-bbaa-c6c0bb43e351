export declare enum RuntimePlatform {
    UNKNOWN = "UNKNOWN",
    EDITOR_PAGE = "EDITOR_PAGE",
    EDITOR_CORE = "EDITOR_CORE",
    MOBILE_BROWSER = "MO<PERSON>LE_BROWSER",
    DESKTOP_BROWSER = "DESKTOP_BROWSER",
    WIN32 = "WIN32",
    ANDROID = "ANDROID",
    IOS = "IOS",
    MACOS = "MACOS",
    OHOS = "OHOS",
    OPENHARMONY = "OPENHARMONY",
    WECHAT_GAME = "WECHAT_GAME",
    WECHAT_MINI_PROGRAM = "WECHAT_MINI_PROGRAM",
    BAIDU_MINI_GAME = "BAIDU_MINI_GAME",
    XIAOMI_QUICK_GAME = "XIAOMI_QUICK_GAME",
    ALIPAY_MINI_GAME = "ALIPAY_MINI_GAME",
    TAOBAO_CREATIVE_APP = "TAOBAO_CREATIVE_APP",
    TAOBAO_MINI_GAME = "TAOBAO_MINI_GAME",
    BYTEDANCE_MINI_GAME = "BYTEDANCE_MINI_GAME",
    OPPO_MINI_GAME = "OPPO_MINI_GAME",
    VIVO_MINI_GAME = "VIVO_MINI_GAME",
    HUAWEI_QUICK_GAME = "HUAWEI_QUICK_GAME",
    COCOSPLAY = "COCOSPLAY",
    LINKSURE_MINI_GAME = "LINKSURE_MINI_GAME",
    QTT_MINI_GAME = "QTT_MINI_GAME"
}
declare class _Device {
    get runtimePlatform(): any;
    get os(): any;
    get networkType(): any;
    get deviceId(): string;
    _isNative: boolean | null;
    isNative(): boolean;
    isMobile(): boolean;
    isWeChat(): boolean;
    isByteDance(): boolean;
    isBrowser(): boolean;
    isMiniGame(): boolean;
    isIOS(): boolean;
    isAndroid(): boolean;
}
export declare const Device: _Device;
export {};
