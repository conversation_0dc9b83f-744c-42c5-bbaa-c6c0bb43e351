export declare enum Module {
    THINKING = "thinking",
    GRAVITY = "gravity",
    WECHAT = "wechat",
    BYTE_DANCE = "byteDance"
}
export declare enum AdMedia {
    WECHAT = "wechat",// 广点通
    BYTE_DANCE = "byteDance"
}
export declare enum AdType {
    REWARD = "reward",
    INTERSTITIAL = "interstitial",
    REWARD_INTERSTITIAL = "reward_interstitial"
}
interface ThinkingData {
    server_url: string;
    app_id: string;
    is_error_tracker?: boolean;
}
interface Gravity {
    access_token: string;
    name: "ge";
    debug: boolean;
}
interface WeChat {
    app_id: string;
    billing_env?: number;
    billing_qrcode_image_url?: string;
}
interface ByteDance {
    app_id: string;
    billing_env: number;
    ad?: {};
}
export interface Config {
    thinking: ThinkingData[];
    gravity: Gravity;
    wechat?: WeChat;
    byteDance?: ByteDance;
}
export declare let ZeusConfig: {
    [index: string]: Config;
};
export declare function getConfig(): Config;
export declare function getModuleConfigValue(module_name: string, module_sub_key: string): any;
export {};
