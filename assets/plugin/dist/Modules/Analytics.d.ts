export declare class Analytics {
    private static instance;
    private _errorTrackerAppId;
    private _sessionId;
    private _config;
    private get config();
    private constructor();
    get SessionId(): string;
    static getInstance(): Analytics;
    setSuperProperties(properties: any): void;
    userSet(properties: any): void;
    userSetOnce(properties: any): void;
    login(accountId: string): void;
    getDistinctId(): string;
    trackEvent(eventName: string, properties: any): void;
    trackRevenue(eventName: string, price: number, currency: string, properties: any): void;
    trackError(eventName: string, properties: any): void;
}
