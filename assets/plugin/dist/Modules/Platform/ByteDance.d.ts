import { BillingParams } from "../../Modules/Platform/Typing";
import Login from "../../Core/Runtime/Result/Login";
import { Action } from "../../Core/Runtime/Action";
import Base from "../../Core/Runtime/Result/Base";
import Ad from "../../Core/Runtime/Result/Advertiser";
import Network from "../../Core/Runtime/Result/Network";
import Questionnaire from "../../Core/Runtime/Result/Questionnaire";
import Notification from "../../Core/Runtime/Result/Notification";
import { BaseAdapter, ShareOptions } from "../../Modules/Platform/BasePlatform";
import { InitResult } from "../../Core/Runtime/Result/init";
export interface RankData {
    dataType: number;
    value: string;
    priority: number;
    zoneId: string;
    success: Function;
    fail: Function;
}
export interface ShowRankListData {
    dataType: number;
    relationType: string;
    rankType: string;
    suffix: string;
    rankTitle: string;
    zoneId: string;
    success: Function;
    fail: Function;
}
export declare class ByteDanceAdapter extends BaseAdapter {
    private readonly tt;
    private readonly loginType;
    private _deviceInfo;
    private _adManager;
    private _analytics;
    private static instance;
    private readonly source_name;
    private _isLogin;
    private _isInit;
    private constructor();
    private get deviceInfo();
    get platform(): any;
    static getInstance(): ByteDanceAdapter;
    init(callback: Action<Base>): void;
    login(callback: Action<InitResult | Login>): void;
    private doLogin;
    private loginSuccess;
    private doGetUserInfo;
    getUserInfo(callback: Action<Base>): void;
    private normalizeByteDanceBillingResponse;
    private androidBillingProcess;
    private iosBillingProcess;
    private unsupportedBillingProcess;
    billing(billingParams: BillingParams, callback: Action<Base>): void;
    showRewardedAd(placement: string, callback: Action<Ad>): void;
    showInterstitialAd(placement: string, callback: Action<Ad>): void;
    onNetworkStatusChange(callback: Action<Network>): void;
    openCustomerService(callback: Action<Base>): void;
    openQuestionnaire(activityId: string, callback: Action<Questionnaire>): void;
    checkUpdate(): void;
    setRankData(rankData: object): void;
    showRankList(option: object): void;
    share(options: ShareOptions, callback: Action<Base>): void;
    nativeToScene(): void;
    subscribeMessage(tmplIds: string[], callback: Action<Notification>): void;
    hideOpenData(): void;
}
