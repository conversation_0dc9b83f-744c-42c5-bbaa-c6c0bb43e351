import { Action } from "../../../Core/Runtime/Action";
import Ad from "../../../Core/Runtime/Result/Advertiser";
import { AdMedia } from "../../../Common/ZeusConfig";
export default class AdManager {
    private readonly media;
    protected readonly mediaType: string;
    private readonly rewardAdUnitId;
    private readonly interstitialAdUnitId;
    private rewardedVideoAd;
    private interstitialAd;
    protected _type: string;
    private _uuid;
    protected _callback: Action<Ad>;
    constructor(media: any, media_type: AdMedia);
    private init;
    private initRewardedVideoAd;
    private initInterstitialAd;
    showRewardedAd(placement: string, callback: Action<Ad>): void;
    private showRewardedVideoAdWithRetry;
    showInterstitialAd(placement: string, callback: Action<Ad>): void;
    protected complete(adUnitId: string, resp: any): void;
    private cancel;
}
