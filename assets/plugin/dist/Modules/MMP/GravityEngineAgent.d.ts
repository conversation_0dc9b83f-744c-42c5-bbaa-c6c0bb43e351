export declare class GravityEngineAgent {
    private static instance;
    private ge;
    static getInstance(): GravityEngineAgent;
    private constructor();
    setIdentifier(openid: string, extraParams: any): void;
    setSuperProperties(properties: any): void;
    trackRegister(): void;
    trackEvent(eventName: string, properties: any): void;
    trackRevenue(amount: number, currency: string, orderId: string, properties: any): void;
    trackShowAd(adType: string, adUnitId: string, properties: any): void;
}
