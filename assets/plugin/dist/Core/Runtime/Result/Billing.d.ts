import Status from "../../../Core/Runtime/Result/Status";
import { BaseCode } from "../../../Core/Runtime/Result/Base";
export declare class BillingCode extends BaseCode {
    static ProductTypeError: Status;
    static BillingServiceDisconnected: Status;
    static DataParseError2: Status;
    static OrderInfoEmpty: Status;
    static CreateOrderFail: Status;
    static NotSupportSubscription: Status;
    static BillingClientNotReady: Status;
    static NotQuerySKUDetail: Status;
    static StartPayFail: Status;
    static PayVerifyFail: Status;
    static PaySuccessConsumeFail: Status;
    static SystemNotSupport: Status;
    static JsApiUnknownResult: Status;
}
