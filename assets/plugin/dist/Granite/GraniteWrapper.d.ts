import { BaseAdapter } from "../Modules/Platform/BasePlatform";
import { InitResult } from "../Core/Runtime/Result/init";
import { Action } from "../Core/Runtime/Action";
import { AppModel } from "../Helper/GlobalData";
export declare class GraniteWrapper {
    static AppPlatform: BaseAdapter;
    static Analytics: any;
    static Attribution: any;
    static init(appModel: AppModel, callback: Action<InitResult>): void;
    static baseInit(callback: Action<InitResult>): void;
    static getVersion(): string;
    private static initComponents;
}
