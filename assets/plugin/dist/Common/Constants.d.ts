export default class AccountConstants {
    static readonly LOCAL_ACCOUNT_KEY: string;
    static readonly LOCAL_ID_KEY: string;
}
export declare class BIEvent {
    static readonly REGISTER_EVENT: string;
    static readonly LOGIN_EVENT: string;
    static readonly BATTLE_EVENT: string;
    static readonly AD_EVENT: string;
    static readonly GUIDE_EVENT: string;
}
export declare class AdFlow {
    static readonly AD_CLICKED: number;
    static readonly AD_SHOWED: number;
    static readonly AD_COMPLETE: number;
}
export declare enum LoginType {
    Wechat = 9,
    DouYin = 13
}
