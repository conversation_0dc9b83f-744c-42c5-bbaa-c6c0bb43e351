interface HasherState {
    buffer: string;
    buflen: number;
    length: number;
    state: number[];
}
export declare class Md5 {
    /**
     * Hash a UTF-8 string on the spot
     * @param str String to hash
     * @param raw Whether to return the value as an `Int32Array`
     */
    static hashStr(str: string, raw?: false): string;
    static hashStr(str: string, raw: true): Int32Array;
    /**
     * Hash a ASCII string on the spot
     * @param str String to hash
     * @param raw Whether to return the value as an `Int32Array`
     */
    static hashAsciiStr(str: string, raw?: false): string;
    static hashAsciiStr(str: string, raw: true): Int32Array;
    private static stateIdentity;
    private static buffer32Identity;
    private static hexChars;
    private static hexOut;
    private static onePassHasher;
    private static _hex;
    private static _md5cycle;
    private _dataLength;
    private _bufferLength;
    private _state;
    private _buffer;
    private _buffer8;
    private _buffer32;
    constructor();
    /**
     * Initialise buffer to be hashed
     */
    start(): this;
    /**
     * Append a UTF-8 string to the hash buffer
     * @param str String to append
     */
    appendStr(str: string): this;
    /**
     * Append an ASCII string to the hash buffer
     * @param str String to append
     */
    appendAsciiStr(str: string): this;
    /**
     * Append a byte array to the hash buffer
     * @param input array to append
     */
    appendByteArray(input: Uint8Array): this;
    /**
     * Get the state of the hash buffer
     */
    getState(): HasherState;
    /**
     * Override the current state of the hash buffer
     * @param state New hash buffer state
     */
    setState(state: HasherState): void;
    /**
     * Hash the current state of the hash buffer and return the result
     * @param raw Whether to return the value as an `Int32Array`
     */
    end(raw?: boolean): string | Int32Array<ArrayBufferLike>;
}
export {};
