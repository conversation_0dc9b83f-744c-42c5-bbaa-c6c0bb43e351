export interface WechatADConfig {
    rewardAdUnitId: string;
    interstitialAdUnitId: string;
}
export interface DouYinADConfig {
    rewardAdUnitId: string;
    interstitialAdUnitId: string;
}
export interface AppModel {
    AppId: string;
    AppKey: string;
    AppRequestURL: string;
    UserProtocolURL: string;
    PrivacyProtocolURL: string;
    DebugMode: boolean;
}
export declare class GlobalContainer {
    private static instance;
    private WechatADConfigKey;
    private DouYinADConfigKey;
    private AppModelKey;
    private constructor();
    static getInstance(): GlobalContainer;
    setWechatADConfig(config: WechatADConfig): void;
    getWechatADConfig(): WechatADConfig;
    setDouYinADConfig(config: DouYinADConfig): void;
    getDouYinADConfig(): DouYinADConfig;
    setAppModel(appModel: AppModel): void;
    getAppModel(): AppModel;
    private set;
    private get;
    private delete;
}
