import {_decorator, Component} from 'cc';
import { GraniteWrapper, setConfig } from './plugin/dist/index.js';

const {ccclass} = _decorator;
// import {ByteDanceAdapter} from "./SDK/Modules/Platform/ByteDance";
// import Logger from "./Common/Logger";
// import {BillingParams} from "./Modules/Platform/Typing";

@ccclass('ByteDanceSDK')
export class ByteDanceSDK extends Component {
    start() {
        const config = {
            thinking: [
                {
                    server_url: "https://oversea-log.topjoy.com/",
                    app_id: "226d1c95988343a78f35131292ef56af",
                },
                {
                    server_url: "https://oversea-log.topjoy.com/",
                    app_id: "226d1c95988343a78f35131292ef56af",
                    is_error_tracker: true
                },
            ],
            wechat: {
                app_id: "wx05dd926e19017db6",
                billing_env: 0,  // 0 米大师正式环境 // 1 米大师沙箱环境
                billing_qrcode_image_url: 'https://pandora-packages.oss-cn-beijing.aliyuncs.com/client/picture/clickpay.wad_0.20240304184623.png',
            },
            gravity: {
                access_token: "g8jX3lmadU2YHafqsqKruevunttyycop",
                name: "ge",
                debug: false
            },
            byteDance: {
                app_id: "tt0eeb785691dcd5dc02",
                billing_env: 0,
            }
        }
        setConfig(config);
    }

    update(deltaTime: number) {

    }


    byteDanceCustomerService() {
        console.log("ByteDanceSDK", "byteDanceCustomerService")
        GraniteWrapper.AppPlatform.openCustomerService((res) => {
            console.log(JSON.stringify(res, null, 2));

        });
    }

    byteDanceGetGameInfo() {
        console.log("ByteDanceSDK", "byteDanceGetGameInfo")
        const appModel = {
            AppId: '7AVS2D5QH2TV',
            AppKey: 'DF864TCE1XWZE1NH',
            AppRequestURL: 'https://zeus-cn.topjoy.com',
            UserProtocolURL: '',
            PrivacyProtocolURL: '',
            DebugMode: true,
        }
        GraniteWrapper.init(appModel, (res) => {
            console.log(JSON.stringify(res, null, 2));
        });
    }

    byteDanceQuestionnaire() {
        console.log("ByteDanceSDK", "byteDanceQuestionnaire");
        GraniteWrapper.AppPlatform.openQuestionnaire("wxLeqv4", (res) => {
            console.log("ByteDanceSDK", "callback result", res);
            console.log(JSON.stringify(res, null, 2));
        });
    };

    byteDanceLogin() {
        console.log("ByteDanceSDK", "byteDanceLogin");
        GraniteWrapper.AppPlatform.login((res) => {
            console.log("ByteDanceSDK", "callback result", res);
            console.log(JSON.stringify(res, null, 2));

        });
    };

    byteDanceBilling() {
        console.log("ByteDanceSDK", "byteDanceBilling");
        const params = {
            productName: "",
            productId: "com.fairy.wechat.package.18",
            roleId: 'test_roleid',
            roleName: 'test_rolename',
            price: 10,
            currency: "CNY",
            billingType: "",
            billingEnv: 1,
            billingCallbackUrl: "",
            platform: "",
        };
        GraniteWrapper.AppPlatform.billing(params, (res) => {
            console.log("ByteDanceSDK", "callback result", res);
            console.log(JSON.stringify(res, null, 2));

        });
    };

    byteDanceCheckUpdate() {
        console.log("ByteDanceSDK", "byteDanceCheckUpdate");
        GraniteWrapper.AppPlatform.checkUpdate();
    };

    byteDanceGetUserInfo() {
        console.log("ByteDanceSDK", "byteDanceGetUserInfo");

        GraniteWrapper.AppPlatform.getUserInfo((res) => {
            console.log("byteDanceGetUserInfo", "callback result", res);
            console.log(JSON.stringify(res, null, 2));
        });
    };

    byteDanceShowRankList() {
        console.log("ByteDanceSDK", "byteDanceShowRankList");
        const data = {
            dataType: 0,
            relationType: "all",
            rankType: "day",
            suffix: "",
            rankTitle: "",
            zoneId: ""
        };
        GraniteWrapper.AppPlatform.showRankList(data);
    };

    byteDanceSetRankData() {
        console.log("ByteDanceSDK", "byteDanceSetRankData");
        const data = {
            dataType: 0,
            value: "120",
            priority: 0,
            zoneId: ""
        };
        GraniteWrapper.AppPlatform.setRankData(data);
    };

    byteDanceShowRewardedAd() {
        console.log("ByteDanceSDK", "byteDanceShowRewardedAd");
        GraniteWrapper.AppPlatform.showRewardedAd("", (res) => {
            console.log("byteDanceShowRewardedAd callback", res);
            console.log(JSON.stringify(res, null, 2));
        });
    };

    byteDanceShowInterstitialAd() {
        console.log("ByteDanceSDK", "byteDanceShowInterstitialAd");
        GraniteWrapper.AppPlatform.showInterstitialAd("", (res) => {
            console.log("byteDanceShowInterstitialAd callback", res);
            console.log(JSON.stringify(res, null, 2));
        });
    };

    byteDanceShare() {
        console.log("ByteDanceSDK", "byteDanceShare");
        const options = {
            title: 'test title'
        };
        GraniteWrapper.AppPlatform.share(options, (res) => {
            console.log("share callback", res);
            console.log(JSON.stringify(res, null, 2));
        });
    };

    byteDanceNativeToScene() {
        console.log("ByteDanceSDK", "byteDanceNativeToScene");

        GraniteWrapper.AppPlatform.nativeToScene();
    };

    byteDanceSubscribeMessage() {
        console.log("ByteDanceSDK", "byteDanceSubscribeMessage");

        GraniteWrapper.AppPlatform.subscribeMessage(['MSG1835213137217498660703721556224'], (res) => {
            console.log('byteDanceSubscribeMessage callback', res);
            console.log(JSON.stringify(res, null, 2));
        });
    };

}

